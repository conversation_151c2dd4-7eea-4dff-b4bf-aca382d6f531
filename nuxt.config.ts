// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: false },
  modules: ['@nuxtjs/tailwindcss', 'nuxt-security'],

  // SECURE configuration with route-specific rules
  security: {
    headers: {
      contentSecurityPolicy: {
        // Base security - very restrictive
        'default-src': ["'self'"],
        'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Nuxt needs these
        'style-src': ["'self'", "'unsafe-inline'"], // Tailwind needs unsafe-inline
        'font-src': ["'self'", "data:"],
        'img-src': ["'self'", "data:", "blob:"],
        'object-src': ["'self'", "data:", "blob:"],
        'media-src': ["'self'", "data:", "blob:"],
        'base-uri': ["'self'"],
        'form-action': ["'self'", "https://surgassists-connect.online"],
        'upgrade-insecure-requests': true,
        
        // More permissive for iframe and connections to allow Documenso
        'frame-src': [
          "'self'", 
          "https://*.documenso.com", 
          "https://documenso.com",
          "https://s3.eu-central-1.amazonaws.com",
          "data:", 
          "blob:",
          "*" // Very permissive - use cautiously
        ],
        'child-src': ["'self'", "https:", "data:", "blob:"],
        'connect-src': ["'self'", "https:", "wss:", "data:", "blob:"],
      },

      // Additional security headers
      xFrameOptions: 'SAMEORIGIN',
      xContentTypeOptions: 'nosniff',
      referrerPolicy: 'strict-origin-when-cross-origin',

      // Disable COEP to allow Documenso iframe embedding
      crossOriginEmbedderPolicy: false,
    },

    // Enable additional security features
    rateLimiter: {
      tokensPerInterval: 150,
      interval: 300000, // 5 minutes
    },

    // CSRF protection with exceptions for API routes
    csrf: {
      methodsToProtect: ['POST', 'PUT', 'PATCH', 'DELETE'],
      excludedUrls: [
        '/api/update-profile-consultant',
        '/api/documents-download/**',
        '/api/pdf-documents/**'
      ]
    },

    // Hide powered by headers
    hidePoweredBy: true,
  },

  

  // Runtime config for environment variables
  runtimeConfig: {
    // Private keys (only available on server-side)
    goBackendApi: process.env.GO_BACKEND_API,
    apiKey: process.env.API_KEY,

    // public keys (exposed to client-side)
    public: {
      appwriteEndpoint: process.env.NUXT_APPWRITE_ENDPOINT || 'https://fra.cloud.appwrite.io/v1',
      appwriteProjectId: process.env.NUXT_APPWRITE_PROJECT_ID || '',
      appwriteDatabaseId: process.env.NUXT_APPWRITE_DATABASE_ID || '6852bce800170b4ea7b4',
      appwriteProfileCollectionId: process.env.NUXT_APPWRITE_PROFILE_COLLECTION_ID || '6852be26001922ea0c85',
      appwriteLogCollectionId: process.env.NUXT_APPWRITE_LOG_COLLECTION_ID || '6852d0fa00256ad3188b',
      appwriteConsentCollectionId: process.env.NUXT_APPWRITE_CONSENT_COLLECTION_ID || '6853de08003879050263',
      appwriteReportsCollectionId: process.env.NUXT_APPWRITE_REPORTS_COLLECTION_ID || '68540de400285bfcb5b6',
      appwritePatientsCollectionId: process.env.NUXT_APPWRITE_PATIENTS_COLLECTION_ID || '6887435d000eae4f6123',
      appwriteDocumentStorageBucketId: process.env.NUXT_APPWRITE_DOCUMENT_STORAGE_BUCKET_ID || '688b593f000fe254dd5a',
      appwriteDocumentsUploadCollectionId: process.env.NUXT_APPWRITE_DOCUMENTS_UPLOAD_COLLECTION_ID || '688b5c0c00381f295042'
    }
  }
})