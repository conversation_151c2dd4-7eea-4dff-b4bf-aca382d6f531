export default defineEventHandler(async (event) => {
  try {
    // Get the QR ID from the URL parameter
    const qrId = getRouterParam(event, 'id')
    
    if (!qrId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'QR ID is required'
      })
    }

    console.log('=== FETCHING QR CODE FROM GO BACKEND ===')
    console.log('QR ID:', qrId)

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()
    
    // Validate server configuration
    if (!config.goBackendApi || !config.apiKey) {
      console.error('Missing GO_BACKEND_API or API_KEY in environment variables')
      throw createError({
        statusCode: 500,
        statusMessage: 'Server configuration error'
      })
    }

    console.log('GO Backend URL:', config.goBackendApi + `/make-qr-code/${qrId}`)

    // Fetch QR code data from GO backend
    const qrData = await $fetch(`/make-qr-code/${qrId}`, {
      baseURL: config.goBackendApi,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'X-API-Key': config.apiKey,
        'Content-Type': 'application/json'
      }
    })

    console.log('QR code data from GO backend:', {
      message: qrData.message,
      patient_id: qrData.patient_id,
      qr_code_url: qrData.qr_code_url ? 'URL provided' : 'No URL',
      qr_code_base64: qrData.qr_code_base64 ? 'Base64 provided' : 'No Base64',
      qr_code_size: qrData.qr_code_size
    })
    console.log('=== GO BACKEND QR FETCH SUCCESS ===')

    // Return the QR code data
    return {
      success: true,
      data: qrData
    }

  } catch (error) {
    console.error('=== GO BACKEND QR FETCH ERROR ===')
    console.error('Error details:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data
    })

    // Handle specific GO backend HTTP status codes
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'QR code not found'
      })
    } else if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid QR code ID'
      })
    } else if (error.statusCode === 401 || error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access denied'
      })
    } else if (error.statusCode >= 400 && error.statusCode < 500) {
      // Any other 4xx client error
      throw createError({
        statusCode: error.statusCode,
        statusMessage: 'Not found'
      })
    } else if (error.statusCode >= 500) {
      // 5xx server errors
      throw createError({
        statusCode: 500,
        statusMessage: 'Server error occurred'
      })
    } else {
      // Network or other errors
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to fetch QR code from backend'
      })
    }
  }
})
