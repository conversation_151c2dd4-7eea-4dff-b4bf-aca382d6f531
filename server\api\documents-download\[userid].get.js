export default defineEventHandler(async (event) => {
  try {
    const userId = getRouterParam(event, 'userid')
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    const response = await $fetch(`${config.goBackendApi}/get-list-of-documents-for-download/${userId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    })

    return response

  } catch (error) {
    // Handle error silently
    throw createError({
      statusCode: error.status || 500,
      statusMessage: error.message || 'Failed to fetch documents for download'
    })
  }
})
