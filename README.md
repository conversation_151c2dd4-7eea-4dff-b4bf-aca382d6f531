# Surgassists AR Portal

A comprehensive medical consent management system with AR integration, built with Nuxt 3 and Appwrite.

## 🏥 System Overview

The Surgassists AR Portal manages the complete patient consent workflow from initial consent creation to VR app viewing, with secure document signing and QR code generation.

## 🔄 Technical Workflow: Patients Consent to QR Code

### Phase 1: Consent Creation (`/patients-consent`)

#### 1.1 User Authentication
```javascript
// Authentication middleware protects the route
definePageMeta({ middleware: 'auth' })

// Only logged-in consultants can access
const { isLoggedIn, currentUser } = useAuth()
```

#### 1.2 Consent Form Submission
```javascript
// Form data collection
const formData = {
  patientEmail: '<EMAIL>',
  procedureType: 'Hysteroscopy', // Options: Hysteroscopy, Flexiblecystos, KidneyStones
  userId: currentUser.value.$id
}

// Database save to Appwrite
await appwriteService.createConsent(
  currentUser.value.$id,    // UserID
  patientEmail.value,       // Email
  procedureType.value,      // Procedure
  'Started'                 // Initial Status
)
```

#### 1.3 Dual Backend Integration
```javascript
// 1. Save to Appwrite (Consent Collection: 6853de08003879050263)
const consentRecord = await databases.createDocument(
  databaseId,
  consentCollectionId,
  'unique()',
  {
    UserID: userId,
    Email: email,
    Procedure: procedure,
    Status: 'Started'
  }
)

// 2. Notify GO Backend via secure server API
await $fetch('/api/consent/start', {
  method: 'POST',
  body: { consentRecord: savedRecord }
})
```

### Phase 2: Server-Side GO Backend Communication

#### 2.1 Secure API Route (`/server/api/consent/start.post.js`)
```javascript
// Server-only environment variables
const config = useRuntimeConfig()
const { goBackendApi, apiKey } = config // Private server keys

// POST to GO backend
const response = await $fetch('/start-patient-consent', {
  baseURL: goBackendApi, // https://surgassistsbackendar.online
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'X-API-Key': apiKey,
    'Content-Type': 'application/json'
  },
  body: consentRecord
})
```

#### 2.2 GO Backend Processing
```go
// GO backend receives consent data
POST /start-patient-consent
{
  "$id": "685920258e9218bf5e89",
  "UserID": "685285ef0004efcf2ba9",
  "Email": "<EMAIL>",
  "Procedure": "Hysteroscopy",
  "Status": "Started",
  "$createdAt": "2024-01-15T10:30:00.000Z"
}

// GO backend updates status to "Email Sent"
// Processes patient notification
// Prepares for next workflow steps
```

### Phase 3: Status Progression Workflow

#### 3.1 Status Flow
```
Started → Email Sent → Patient Open Form → Patient Consent Done → QR Generated → VR App Viewed
```

#### 3.2 Dynamic Status Display
```javascript
// Status colors and progression tracking
const getStatusColor = (status) => {
  switch (status) {
    case 'Started': return 'bg-blue-100 text-blue-800'
    case 'Email Sent': return 'bg-orange-100 text-orange-800'
    case 'Patient Open Form': return 'bg-yellow-100 text-yellow-800'
    case 'Patient Consent Done': return 'bg-green-100 text-green-800'
    case 'QR Generated': return 'bg-indigo-100 text-indigo-800'
    case 'VR App Viewed': return 'bg-purple-100 text-purple-800'
  }
}
```

### Phase 4: Patient Journey (`/patient/[id]`)

#### 4.1 Public Patient Access
```javascript
// No authentication required for patient pages
definePageMeta({ auth: false })

// Server-side data fetching for security
const response = await $fetch(`/api/patient/${patientId}`)
```

#### 4.2 GO Backend Patient Data Retrieval
```javascript
// Server API calls GO backend
GET /patient-consent-viewed/:id

// Returns nested document structure
{
  "document": {
    "$id": "685920258e9218bf5e89",
    "Email": "<EMAIL>",
    "Status": "Patient Consent Done",
    "Procedure": "Hysteroscopy"
  },
  "message": "Patient consent viewed successfully"
}
```

#### 4.3 Video + Signing Interface
```html
<!-- Video player with fullscreen support -->
<video controls allowfullscreen webkit-playsinline playsinline>
  <source src="/videos/temp.mp4" type="video/mp4">
</video>

<!-- Sign consent button -->
<button @click="handleSignConsent" :disabled="isSigningInProgress">
  {{ isSigningInProgress ? 'Processing...' : 'Sign Consent' }}
</button>
```

### Phase 5: Digital Signature Process

#### 5.1 Sign Consent Trigger
```javascript
const handleSignConsent = async () => {
  // Prevent multiple clicks
  isSigningInProgress.value = true

  // Call GO backend signature endpoint
  const response = await $fetch(`/api/patient-signature/${patientId}`)

  // Show success message
  alert('Your documents are ready to sign and will be emailed to you shortly.')

  // Auto-redirect after 2 seconds
  setTimeout(() => {
    navigateTo('/thanks')
  }, 2000)
}
```

#### 5.2 GO Backend Signature Processing
```javascript
// Server API route: /server/api/patient-signature/[id].get.js
GET /patient-started-signature/:id

// GO backend processes signature request
// Updates consent status
// Prepares documents for email delivery
```

### Phase 6: QR Code Generation

#### 6.1 QR Code Request
```javascript
// When GO backend is ready to generate QR
// Status updates to "QR Generated"
// QR code becomes available at /qr-display/[id]
```

#### 6.2 QR Display System (`/qr-display/[id]`)
```javascript
// Public QR access (no authentication)
definePageMeta({ auth: false })

// Server-side QR data fetching
const qrData = await $fetch(`/api/qr/${qrId}`)

// GO backend QR endpoint
GET /make-qr-code/:id
{
  "message": "QR code generated successfully",
  "patient_id": "685920258e9218bf5e89",
  "qr_code_url": "https://example.com/qr.png",
  "qr_code_base64": "iVBORw0KGgoAAAANSUhEUgAA...",
  "qr_code_size": 2048
}
```

#### 6.3 QR Code Display
```html
<!-- Centered QR code display -->
<div class="min-h-screen flex items-center justify-center">
  <img
    :src="`data:image/png;base64,${qrData.qr_code_base64}`"
    class="mx-auto max-w-lg w-full h-auto"
    alt="QR Code for Patient"
  />
</div>
```

## 🗄️ Database Architecture

### Appwrite Collections

#### Consent Collection (`6853de08003879050263`)
```javascript
{
  "$id": "unique_document_id",
  "UserID": "consultant_user_id",     // Links to consultant
  "Email": "<EMAIL>",     // Patient email
  "Procedure": "Hysteroscopy",        // Medical procedure
  "Status": "QR Generated",           // Workflow status
  "$createdAt": "timestamp",
  "$updatedAt": "timestamp"
}
```

#### Reports Collection (`68540de400285bfcb5b6`)
```javascript
{
  "$id": "unique_document_id",
  "UserID": "consultant_user_id",     // Links to consultant
  "Title": "Report Title",            // Report information
  "$createdAt": "timestamp"
}
```

#### Profile Collection (`6852be26001922ea0c85`)
```javascript
{
  "$id": "unique_document_id",
  "UserID": "consultant_user_id",
  "ConsultantName": "Dr. Smith",
  "Email": "<EMAIL>",   // Email to receive documents
  "Address1": "123 Medical Street",
  "Address2": "Suite 100",
  "PostCode": "SW1A 1AA",
  "Telephone": "+44 123 456 789"
}
```