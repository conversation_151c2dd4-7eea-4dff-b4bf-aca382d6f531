export default defineEventHandler(async (event) => {
  try {
    // Get the patient ID from the URL parameter
    const patientId = getRouterParam(event, 'id')

    if (!patientId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Patient ID is required'
      })
    }

    console.log('=== FETCHING PATIENT INFO FOR THANK YOU PAGE ===')
    console.log('Patient ID:', patientId)

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    // Import Appwrite with server credentials
    const { Client, Databases } = await import('appwrite')

    // Initialize Appwrite client with server credentials
    const client = new Client()
    client
      .setEndpoint(config.public.appwriteEndpoint)
      .setProject(config.public.appwriteProjectId)
      .setKey(config.appwriteApiKey) // Server-side API key

    const databases = new Databases(client)

    console.log('Appwrite client initialized for server-side access')

    // Step 1: Get patient by ID
    console.log('Step 1: Loading patient from collection:', config.public.appwritePatientsCollectionId)
    
    const patient = await databases.getDocument(
      config.public.appwriteDatabaseId,
      config.public.appwritePatientsCollectionId,
      patientId
    )

    console.log('Patient loaded:', patient)
    console.log('LoginUserID from patient:', patient.LoginUserID)

    let consultantProfile = null

    // Step 2: Get consultant profile using LoginUserID
    if (patient.LoginUserID) {
      console.log('Step 2: Loading consultant profile for UserID:', patient.LoginUserID)
      
      // Query Profile collection by UserID
      const profileResponse = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteProfileCollectionId,
        [
          // Query where UserID equals the LoginUserID from patient
          `equal("UserID", "${patient.LoginUserID}")`
        ]
      )

      if (profileResponse.documents.length > 0) {
        consultantProfile = profileResponse.documents[0]
        console.log('Consultant profile loaded:', consultantProfile)
        console.log('Profile fields available:', Object.keys(consultantProfile))
        console.log('Telephone field:', consultantProfile.Telephone)
        console.log('Email field:', consultantProfile.Email)
      } else {
        console.log('No profile found for consultant UserID:', patient.LoginUserID)
      }
    }

    // Return both patient and consultant data
    return {
      success: true,
      patient: patient,
      consultantProfile: consultantProfile
    }

  } catch (error) {
    console.error('Error fetching patient info:', error)
    
    throw createError({
      statusCode: error.code === 404 ? 404 : 500,
      statusMessage: error.message || 'Failed to fetch patient information'
    })
  }
})
