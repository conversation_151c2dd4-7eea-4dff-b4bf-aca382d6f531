export default defineEventHandler(async (event) => {
  try {
    // Get the form data from the request body
    const formData = await readBody(event)

    console.log('=== UPDATE PROFILE CONSULTANT SERVER API ===')
    console.log('Received form data:', formData)
    console.log('PostCode value:', `"${formData.PostCode}"`)
    console.log('PostCode length:', formData.PostCode?.length)
    console.log('PostCode type:', typeof formData.PostCode)

    // Check all field lengths and types
    Object.keys(formData).forEach(key => {
      const value = formData[key]
      console.log(`${key}: "${value}" (length: ${value?.length}, type: ${typeof value})`)
    })

    // Validate required fields
    if (!formData) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Form data is required'
      })
    }

    // Clean and validate form data
    const cleanedFormData = {}
    Object.keys(formData).forEach(key => {
      let value = formData[key]

      // Convert to string and trim whitespace
      if (value !== null && value !== undefined) {
        value = String(value).trim()
      }

      // Validate PostCode specifically (max 10 chars)
      if (key === 'PostCode' && value && value.length > 10) {
        console.log(`PostCode too long: "${value}" (${value.length} chars)`)
        value = value.substring(0, 10)
        console.log(`PostCode truncated to: "${value}"`)
      }

      cleanedFormData[key] = value
    })

    console.log('Cleaned form data:', cleanedFormData)

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    console.log('Go Backend API URL:', config.goBackendApi)
    console.log('API Key exists:', !!config.apiKey)
    console.log('Full URL:', `${config.goBackendApi}/update-profile-consultant`)

    // Make PUT request to Go backend with cleaned data
    const response = await $fetch(`${config.goBackendApi}/update-profile-consultant`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: cleanedFormData
    })

    console.log('Go backend response received')
    console.log('Response:', response)

    // Check if response indicates success
    if (response && (response.action === 'updated' || response.action === 'created')) {
      console.log(`Profile ${response.action} successfully:`, response.message)
      
      return {
        success: true,
        action: response.action,
        message: response.message,
        data: response
      }
    } else {
      console.log('Unexpected response format:', response)
      throw createError({
        statusCode: 500,
        statusMessage: 'Unexpected response from backend'
      })
    }

  } catch (error) {
    console.error('Error updating profile consultant:', error)
    console.error('Error status:', error.status)
    console.error('Error message:', error.message)
    console.error('Error data:', error.data)

    // Parse user-friendly error messages (NO technical details)
    let userMessage = 'Unable to save your profile. Please try again.'
    let statusCode = 400 // Always return 400 for user errors, not 500

    if (error.data && error.data.message) {
      const errorMessage = error.data.message

      // Handle specific validation errors with user-friendly messages
      if (errorMessage.includes('PostCode') && errorMessage.includes('no longer than 10 chars')) {
        userMessage = 'Post code is too long. Please enter a shorter post code.'
      } else if (errorMessage.includes('ConsultantName')) {
        userMessage = 'Please enter a valid consultant name.'
      } else if (errorMessage.includes('Address1')) {
        userMessage = 'Please enter a valid address.'
      } else if (errorMessage.includes('Telephone')) {
        userMessage = 'Please enter a valid telephone number.'
      } else if (errorMessage.includes('Email')) {
        userMessage = 'Please enter a valid email address.'
      } else if (errorMessage.includes('invalid type') || errorMessage.includes('document_invalid_structure')) {
        userMessage = 'Please check that all fields are filled out correctly.'
      } else {
        // Generic user-friendly message - never expose technical details
        userMessage = 'Unable to save your profile. Please check your information and try again.'
      }
    }

    throw createError({
      statusCode: statusCode,
      statusMessage: userMessage
    })
  }
})
