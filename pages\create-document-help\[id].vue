<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

    <!-- Back Link -->
    <div class="mb-8">
      <NuxtLink 
        :to="`/create-document/${patientId}`" 
        class="inline-flex items-center text-blue-600 hover:text-blue-700 hover:underline"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Back to Create Document
      </NuxtLink>
    </div>

    <!-- Page Header -->
    <div class="mb-8 text-center">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Document Upload Help</h1>
      <p class="text-lg text-gray-600">Follow these simple steps to upload your medical document</p>
    </div>

    <!-- Step 1: Download PDF -->
    <div class="mb-8">
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-center gap-3">
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-lg">1</span>
          </div>
          <p class="text-lg sm:text-xl lg:text-1xl text-gray-600">
            Download the Medical Form PDF
          </p>
        </div>
      </div>
      
      <div class="py-4"></div>
      
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="bg-white border border-gray-300 rounded-lg shadow-sm p-6 w-full max-w-4xl mx-auto">
          <p class="text-gray-700 leading-relaxed mb-4">
            <span class="font-semibold text-1xl">Download Required Form</span>
            <br>
            Click the button below to download the medical form that needs to be completed.
          </p>
          
          <div class="text-center">
            <a 
              href="/documents/HMedicalForm.pdf" 
              download="HMedicalForm.pdf"
              class="inline-flex items-center px-6 py-3 text-lg font-semibold text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              style="background-color: #65ADEE;"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Download Medical Form PDF
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Fill and Save -->
    <div class="mb-8">
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-center gap-3">
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-lg">2</span>
          </div>
          <p class="text-lg sm:text-xl lg:text-1xl text-gray-600">
            Fill in the Form and Save
          </p>
        </div>
      </div>
      
      <div class="py-4"></div>
      
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="bg-white border border-gray-300 rounded-lg shadow-sm p-6 w-full max-w-4xl mx-auto">
          <p class="text-gray-700 leading-relaxed">
            <span class="font-semibold text-1xl">Complete the Medical Form</span>
            <br>
            Open the downloaded PDF file and fill in all required information. Make sure to:
          </p>
          
          <ul class="mt-4 space-y-2 text-gray-700">
            <li class="flex items-start">
              <span class="text-blue-600 mr-2">•</span>
              Fill in all required fields completely
            </li>
            <li class="flex items-start">
              <span class="text-blue-600 mr-2">•</span>
              Use clear, legible handwriting or type directly in the PDF
            </li>
            <li class="flex items-start">
              <span class="text-blue-600 mr-2">•</span>
              Save the completed form to your computer
            </li>
            <li class="flex items-start">
              <span class="text-blue-600 mr-2">•</span>
              Ensure the file is saved as a PDF format
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Step 3: Go to Upload Page -->
    <div class="mb-8">
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-center gap-3">
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-lg">3</span>
          </div>
          <p class="text-lg sm:text-xl lg:text-1xl text-gray-600">
            Go to Document Upload Page
          </p>
        </div>
      </div>
      
      <div class="py-4"></div>
      
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="bg-white border border-gray-300 rounded-lg shadow-sm p-6 w-full max-w-4xl mx-auto">
          <p class="text-gray-700 leading-relaxed mb-4">
            <span class="font-semibold text-1xl">Navigate to Upload Page</span>
            <br>
            Click the button below to go to the document upload page where you can upload your completed form.
          </p>
          
          <div class="text-center">
            <NuxtLink 
              :to="`/create-document/${patientId}`"
              class="inline-flex items-center px-6 py-3 text-lg font-semibold text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              style="background-color: #65ADEE;"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              Go to Upload Page
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 4: Upload Document -->
    <div class="mb-8">
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-center gap-3">
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-lg">4</span>
          </div>
          <p class="text-lg sm:text-xl lg:text-1xl text-gray-600">
            Upload the Completed Document
          </p>
        </div>
      </div>
      
      <div class="py-4"></div>
      
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="bg-white border border-gray-300 rounded-lg shadow-sm p-6 w-full max-w-4xl mx-auto">
          <p class="text-gray-700 leading-relaxed">
            <span class="font-semibold text-1xl">Upload Your Completed Form</span>
            <br>
            On the upload page, follow these final steps:
          </p>
          
          <ul class="mt-4 space-y-2 text-gray-700">
            <li class="flex items-start">
              <span class="text-blue-600 mr-2">•</span>
              Select the appropriate procedure type from the dropdown
            </li>
            <li class="flex items-start">
              <span class="text-blue-600 mr-2">•</span>
              Drag and drop your completed PDF file or click to select it
            </li>
            <li class="flex items-start">
              <span class="text-blue-600 mr-2">•</span>
              Wait for the upload to complete (you'll see a progress bar)
            </li>
            <li class="flex items-start">
              <span class="text-blue-600 mr-2">•</span>
              Confirm the upload was successful
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Additional Help -->
    <div class="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-blue-800 mb-3">Need Additional Help?</h3>
      <p class="text-blue-700">
        If you encounter any issues during this process, please contact your healthcare provider 
        or the technical support team for assistance.
      </p>
    </div>

  </div>
</template>

<script setup>
// Require authentication to access this page
definePageMeta({
  middleware: 'auth'
})

// Get the patient ID from the URL parameter
const route = useRoute()
const patientId = route.params.id

console.log('Help page loaded with patient ID:', patientId)

// Set page title
useHead({
  title: 'Document Upload Help | Surgassists'
})
</script>
