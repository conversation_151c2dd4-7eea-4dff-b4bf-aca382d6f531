import { ref, readonly } from 'vue'
import { appwriteService } from '~/services/appwrite'

// Global auth state
const isLoggedIn = ref(false)
const currentUser = ref(null)
const isLoading = ref(false)

export const useAuth = () => {
  // Check authentication status
  const checkAuth = async () => {
    try {
      isLoading.value = true
      const user = await appwriteService.getCurrentUser()
      if (user) {
        isLoggedIn.value = true
        currentUser.value = user
      } else {
        isLoggedIn.value = false
        currentUser.value = null
      }
    } catch (error) {
      isLoggedIn.value = false
      currentUser.value = null
    } finally {
      isLoading.value = false
    }
  }

  // Login
  const login = async (email, password) => {
    try {
      isLoading.value = true
      await appwriteService.login(email, password)
      await checkAuth() // Refresh auth state

      // Log the login action
      if (currentUser.value) {
        const { logAction, LOG_ACTIONS } = useLogger()
        await logAction(currentUser.value.$id, LOG_ACTIONS.LOGIN)
      }

      return true
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Logout
  const logout = async () => {
    try {
      isLoading.value = true

      // Log the logout action before clearing user data
      if (currentUser.value) {
        const { logAction, LOG_ACTIONS } = useLogger()
        await logAction(currentUser.value.$id, LOG_ACTIONS.LOGOUT)
      }

      await appwriteService.logout()
      isLoggedIn.value = false
      currentUser.value = null
      return true
    } catch (error) {
      // Even if logout fails, clear local state
      isLoggedIn.value = false
      currentUser.value = null
      throw error
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoggedIn: readonly(isLoggedIn),
    currentUser: readonly(currentUser),
    isLoading: readonly(isLoading),
    checkAuth,
    login,
    logout
  }
}
