export default defineEventHandler(async (event) => {
  try {
    // Get the patient ID from the URL parameter
    const patientId = getRouterParam(event, 'id')

    if (!patientId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Patient ID is required'
      })
    }

    console.log('=== FETCHING PATIENT CONSENT FROM GO BACKEND ===')
    console.log('Patient ID:', patientId)

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    // Validate server configuration
    if (!config.goBackendApi || !config.apiKey) {
      console.error('Missing GO_BACKEND_API or API_KEY in environment variables')
      throw createError({
        statusCode: 500,
        statusMessage: 'Server configuration error'
      })
    }

    console.log('GO Backend URL:', config.goBackendApi + `/patient-consent-viewed/${patientId}`)

    // Fetch consent data from GO backend
    const backendResponse = await $fetch(`/patient-consent-viewed/${patientId}`, {
      baseURL: config.goBackendApi,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'X-API-Key': config.apiKey,
        'Content-Type': 'application/json'
      }
    })

    console.log('Full GO backend response:', backendResponse)

    // Extract the document data from the GO backend response
    const consentRecord = backendResponse.document

    if (!consentRecord) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Consent document not found in backend response'
      })
    }

    console.log('Extracted consent record:', consentRecord)
    console.log('=== GO BACKEND FETCH SUCCESS ===')

    // Return the consent record (just the document part)
    return {
      success: true,
      data: consentRecord
    }

  } catch (error) {
    console.error('=== GO BACKEND FETCH ERROR ===')
    console.error('Error details:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data
    })

    // Handle specific GO backend errors
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Consent record not found'
      })
    }

    // Handle other errors
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch consent record from backend'
    })
  }
})
