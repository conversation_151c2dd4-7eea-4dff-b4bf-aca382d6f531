
<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-100">
    <div class="max-w-md w-full p-6 bg-white rounded-lg shadow-md">
      <h1 class="text-2xl font-bold text-center text-gray-800 mb-6">Login</h1>
      
      <form @submit.prevent="handleLogin" class="space-y-4">
        <!-- Email input -->
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
          <input 
            id="email" 
            v-model="email" 
            type="email" 
            required 
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
        
        <!-- Password input -->
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
          <input 
            id="password" 
            v-model="password" 
            type="password" 
            required 
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="••••••••"
          />
        </div>
        
        <!-- Error message -->
        <div v-if="error" class="text-red-500 text-sm">
          {{ error }}
        </div>
        
        <!-- Submit button -->
        <div>
          <button 
            type="submit" 
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            :disabled="isLoading"
          >
            <span v-if="isLoading">Loading...</span>
            <span v-else>Sign in</span>
          </button>
        </div>
        
    
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// Form data
const email = ref('');
const password = ref('');
const error = ref('');
const router = useRouter();

// Use auth composable
const { login, isLoading, isLoggedIn, checkAuth } = useAuth();

const handleLogin = async () => {
  try {
    error.value = '';

    // Authenticate using composable
    await login(email.value, password.value);

    // Redirect to dashboard after successful login
    router.push('/dashboard');
  } catch (err) {
    error.value = err.message || 'Failed to login. Please try again.';
  }
};

// Check if user is already logged in when component mounts
onMounted(async () => {
  console.log('Login page: Checking authentication status...')
  await checkAuth()

  if (isLoggedIn.value) {
    console.log('User already logged in, redirecting to dashboard')
    router.push('/dashboard')
  } else {
    console.log('User not logged in, showing login form')
  }
})
</script>
