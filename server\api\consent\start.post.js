export default defineEventHandler(async (event) => {
  try {
    // Get the request body
    const body = await readBody(event)
    const { consentRecord } = body

    // Validate required data
    if (!consentRecord) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing consent record data'
      })
    }

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()
    
    // Validate server configuration
    if (!config.goBackendApi || !config.apiKey) {
      console.error('Missing GO_BACKEND_API or API_KEY in environment variables')
      throw createError({
        statusCode: 500,
        statusMessage: 'Server configuration error'
      })
    }

    console.log('=== SENDING TO GO BACKEND ===')
    console.log('Backend URL:', config.goBackendApi + '/start-patient-consent')
    console.log('Consent data:', consentRecord)

    // Send POST request to Go backend
    const response = await $fetch('/start-patient-consent', {
      baseURL: config.goBackendApi,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
        'X-API-Key': config.apiKey
      },
      body: consentRecord
    })

    console.log('Go backend response:', response)
    console.log('=== GO BACKEND SUCCESS ===')

    return {
      success: true,
      message: 'Consent data sent to Go backend successfully',
      backendResponse: response
    }

  } catch (error) {
    console.error('=== GO BACKEND ERROR ===')
    console.error('Error details:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data
    })
    
    // Don't expose internal errors to client
    if (error.statusCode === 400) {
      throw error // Re-throw validation errors
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to communicate with backend service'
    })
  }
})
