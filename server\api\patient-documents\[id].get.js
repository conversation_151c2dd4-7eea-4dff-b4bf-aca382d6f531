export default defineEventHandler(async (event) => {
  try {
    // Get the patient ID from the URL parameter
    const patientId = getRouterParam(event, 'id')

    if (!patientId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Patient ID is required'
      })
    }

    console.log('=== FETCHING PATIENT DOCUMENTS FROM GO BACKEND ===')
    console.log('Patient ID:', patientId)

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    console.log('Go Backend API URL:', config.goBackendApi)
    console.log('API Key exists:', !!config.apiKey)
    console.log('Full URL:', `${config.goBackendApi}/get-documents-upload-list/${patientId}`)

    // Make request to Go backend with proper Bearer Token authentication
    const response = await $fetch(`${config.goBackendApi}/get-documents-upload-list/${patientId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    console.log('Go backend response:', response)
    console.log('Response type:', typeof response)
    console.log('Response keys:', response ? Object.keys(response) : 'No response')

    // Return the documents data
    return {
      success: true,
      documents: response.documents || [],
      count: response.count || 0,
      patientUserId: response.patient_user_id || patientId,
      raw_response: response // For debugging
    }

  } catch (error) {
    console.error('Error fetching patient documents:', error)
    console.error('Error status:', error.status)
    console.error('Error message:', error.message)
    console.error('Error data:', error.data)
    
    throw createError({
      statusCode: error.status || error.statusCode || 500,
      statusMessage: error.message || 'Failed to fetch patient documents'
    })
  }
})
