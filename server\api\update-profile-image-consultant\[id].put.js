export default defineEventHandler(async (event) => {
  try {
    // Get the user ID from the URL parameter
    const userId = getRouterParam(event, 'id')

    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    console.log('=== UPDATE PROFILE IMAGE CONSULTANT SERVER API ===')
    console.log('User ID:', userId)

    // Parse the multipart form data
    const formData = await readMultipartFormData(event)
    
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No image file provided'
      })
    }

    // Find the image file in the form data
    const imageFile = formData.find(item => item.name === 'image')
    
    if (!imageFile) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Image file is required'
      })
    }

    console.log('Image file received:')
    console.log('- Name:', imageFile.filename)
    console.log('- Type:', imageFile.type)
    console.log('- Size:', imageFile.data.length, 'bytes')

    // Validate file type
    if (!imageFile.type || !imageFile.type.startsWith('image/')) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Please upload a valid image file'
      })
    }

    // Validate file size (max 5MB)
    if (imageFile.data.length > 5 * 1024 * 1024) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Image size must be less than 5MB'
      })
    }

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    console.log('Go Backend API URL:', config.goBackendApi)
    console.log('API Key exists:', !!config.apiKey)
    console.log('Full URL:', `${config.goBackendApi}/update-profile-image-consultant/${userId}`)

    // Create FormData for Go backend
    const backendFormData = new FormData()
    
    // Create a Blob from the buffer data
    const blob = new Blob([imageFile.data], { type: imageFile.type })
    backendFormData.append('image', blob, imageFile.filename)

    // Make PUT request to Go backend
    const response = await $fetch(`${config.goBackendApi}/update-profile-image-consultant/${userId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        // Don't set Content-Type - let fetch set it for FormData
      },
      body: backendFormData
    })

    console.log('Go backend response received')
    console.log('Response:', response)

    // Check if response indicates success - handle your Go backend's response format
    if (response && response.message && response.file_id && response.image_url) {
      console.log('Profile image updated successfully:', response.message)
      console.log('Image URL:', response.image_url)
      console.log('File ID:', response.file_id)

      return {
        success: true,
        action: 'updated',
        message: response.message,
        image_url: response.image_url,
        file_id: response.file_id,
        data: response
      }
    } else {
      console.log('Unexpected response format:', response)
      throw createError({
        statusCode: 500,
        statusMessage: 'Unexpected response from backend'
      })
    }

  } catch (error) {
    console.error('Error updating profile image:', error)
    console.error('Error status:', error.status)
    console.error('Error message:', error.message)
    console.error('Error data:', error.data)
    
    // Parse user-friendly error messages (NO technical details)
    let userMessage = 'Unable to upload your profile image. Please try again.'
    let statusCode = 400
    
    if (error.data && error.data.message) {
      const errorMessage = error.data.message
      
      // Handle specific validation errors with user-friendly messages
      if (errorMessage.includes('file size') || errorMessage.includes('too large')) {
        userMessage = 'Image file is too large. Please choose a smaller image.'
      } else if (errorMessage.includes('file type') || errorMessage.includes('invalid format')) {
        userMessage = 'Please upload a valid image file (JPG, PNG, etc.).'
      } else if (errorMessage.includes('not found')) {
        userMessage = 'Profile not found. Please try again.'
      } else {
        // Generic user-friendly message - never expose technical details
        userMessage = 'Unable to upload your profile image. Please try again.'
      }
    }
    
    throw createError({
      statusCode: statusCode,
      statusMessage: userMessage
    })
  }
})
