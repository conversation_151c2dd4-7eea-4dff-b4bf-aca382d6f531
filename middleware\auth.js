export default defineNuxtRouteMiddleware((to, from) => {
  // Check if we're on the client side
  if (process.client) {
    // Use the auth composable to check authentication status
    const { isLoggedIn, checkAuth } = useAuth()
    
    // Check authentication status
    checkAuth()
    
    // If not logged in, redirect to login page
    if (!isLoggedIn.value) {
      console.log('Access denied - redirecting to login')
      return navigateTo('/login')
    }
    
    console.log('Authentication middleware passed - user is logged in')
  }
})
