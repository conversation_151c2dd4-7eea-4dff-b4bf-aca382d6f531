export default defineEventHandler(async (event) => {
  try {
    // Get the user ID from the URL parameter
    const userId = getRouterParam(event, 'userid')

    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    console.log('=== GET PROFILE CONSULTANT SERVER API ===')
    console.log('User ID:', userId)

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    console.log('Go Backend API URL:', config.goBackendApi)
    console.log('API Key exists:', !!config.apiKey)
    console.log('Full URL:', `${config.goBackendApi}/get-profile-consultant/${userId}`)

    // Make GET request to Go backend
    const response = await $fetch(`${config.goBackendApi}/get-profile-consultant/${userId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    console.log('Go backend response received')
    console.log('Response type:', typeof response)
    console.log('Response keys:', response ? Object.keys(response) : 'No response')

    // Return the profile data
    return {
      success: true,
      data: response,
      message: 'Profile loaded successfully'
    }

  } catch (error) {
    console.error('Error fetching profile consultant:', error)
    console.error('Error status:', error.status)
    console.error('Error message:', error.message)
    console.error('Error data:', error.data)
    
    // Handle specific Go backend errors
    if (error.data && error.data.error) {
      throw createError({
        statusCode: error.status || 500,
        statusMessage: error.data.error
      })
    } else {
      throw createError({
        statusCode: error.status || error.statusCode || 500,
        statusMessage: error.message || 'Failed to fetch profile'
      })
    }
  }
})
