import { appwriteService } from '~/services/appwrite'

export const useLogger = () => {
  // Log user actions with IP address
  const logAction = async (userId, action) => {
    try {
      if (!userId || !action) {
        console.warn('Logger: Missing userId or action')
        return
      }

      // Get IP address from a public service
      let ipAddress = '127.0.0.1' // fallback

      try {
        // Use a simple IP detection service
        const ipResponse = await $fetch('https://api.ipify.org?format=json')
        ipAddress = ipResponse.ip || '127.0.0.1'
      } catch (ipError) {
        console.log('Could not fetch external IP, using fallback')
      }

      // Create log with IP address using direct service
      await appwriteService.createLogWithIP(userId, action, ipAddress)
      console.log(`Logged action: ${action} for user: ${userId} from IP: ${ipAddress}`)

    } catch (error) {
      console.error('Logger error:', error)
      // Fallback to basic logging without IP
      try {
        await appwriteService.createLog(userId, action)
        console.log(`Logged action (fallback): ${action} for user: ${userId}`)
      } catch (fallbackError) {
        console.error('Fallback logger error:', fallbackError)
      }
      // Don't throw - logging should never break the app
    }
  }

  // Get user activity logs
  const getUserActivity = async (userId, limit = 10) => {
    try {
      if (!userId) {
        console.warn('Logger: Missing userId for getUserActivity')
        return []
      }

      const logs = await appwriteService.getUserLogs(userId, limit)
      return logs
    } catch (error) {
      console.error('Error fetching user activity:', error)
      return []
    }
  }

  // Clear all user activity logs
  const clearUserActivity = async (userId) => {
    try {
      if (!userId) {
        console.warn('Logger: Missing userId for clearUserActivity')
        return false
      }

      await appwriteService.clearUserLogs(userId)
      console.log(`Cleared all activity logs for user: ${userId}`)
      return true
    } catch (error) {
      console.error('Error clearing user activity:', error)
      throw error
    }
  }

  // Predefined log actions for consistency (max 40 chars)
  const LOG_ACTIONS = {
    LOGIN: 'User logged in',
    LOGOUT: 'User logged out',
    PROFILE_CREATED: 'Profile created',
    PROFILE_UPDATED: 'Profile updated',
    PROFILE_VIEWED: 'Profile viewed',
    DASHBOARD_VIEWED: 'Dashboard viewed',
    PATIENTS_CONSENT_VIEWED: 'Patients consent viewed',
    CONSENT_STARTED: 'Started consent process',
    REPORTS_VIEWED: 'Reports viewed'
  }

  return {
    logAction,
    getUserActivity,
    clearUserActivity,
    LOG_ACTIONS
  }
}
