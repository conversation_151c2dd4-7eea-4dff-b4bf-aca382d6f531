<template>
  <div class="pdf-viewer">
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p class="text-sm text-gray-600">Loading PDF...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <p class="text-red-700">{{ error }}</p>
    </div>

    <!-- PDF Content - Minimal UI -->
    <div v-else class="pdf-content">
      <!-- Simple PDF Display -->
      <object
        :data="pdfDataUrl"
        type="application/pdf"
        class="pdf-object"
        width="100%"
        height="700px"
      >
        <!-- Simple fallback -->
        <div class="pdf-fallback text-center p-8">
          <p class="text-gray-600 mb-4">PDF cannot be displayed inline.</p>
          <a
            :href="pdfDataUrl"
            download="document.pdf"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Download PDF
          </a>
        </div>
      </object>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  pdfData: {
    type: String,
    required: true
  }
})

// State
const loading = ref(true)
const error = ref('')

// Convert base64 to data URL
const pdfDataUrl = computed(() => {
  if (props.pdfData) {
    return `data:application/pdf;base64,${props.pdfData}`
  }
  return null
})

// Open PDF in new tab
const openInNewTab = () => {
  if (pdfDataUrl.value) {
    window.open(pdfDataUrl.value, '_blank')
  }
}

// Watch for PDF data changes
watch(() => props.pdfData, (newData) => {
  if (newData) {
    loading.value = false
    error.value = ''
    console.log('PDF data received, length:', newData.length)
  } else {
    error.value = 'No PDF data provided'
    loading.value = false
  }
}, { immediate: true })
</script>

<style scoped>
.pdf-viewer {
  width: 100%;
}

.pdf-content {
  background: white;
  border-radius: 0.5rem;
  overflow: hidden;
}

.pdf-object {
  display: block;
  border: none;
}

.pdf-fallback {
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pdf-object {
    height: 500px;
  }
}
</style>
