
<template>
  
   <div>

     <div class="w-full" style="background-color: #EFF9FC;">
      <div class="mb-8 text-center" style="background-color: #EFF9FC;">
       <img src="/images/TopImage.png" alt="Surgassists" class="mx-auto max-w-full h-auto" />
      </div>
    </div>



    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-16">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-lg text-gray-600">Loading PDF documents...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center py-16">
      <div class="text-center max-w-2xl mx-auto">
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-8">
          <svg class="mx-auto h-16 w-16 text-yellow-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          <h2 class="text-2xl font-bold text-yellow-800 mb-4">Information Not Available</h2>
          <p class="text-lg text-yellow-700 mb-6">This could happen if:</p>
          <ul class="text-left text-yellow-700 mb-6 space-y-2">
            <li>• The patient consent record doesn't exist</li>
            <li>• The link has expired or is invalid</li>
            <li>• The patient information is not available</li>
          </ul>
          <p class="text-sm text-yellow-600">
            If you believe this is an error, please contact your healthcare provider.
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else-if="pdfDocuments" class="py-8">
      <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Digital Signature</h2>
        <p class="text-lg text-gray-600">Consent ID: {{ consentId }}</p>
      </div>

      <!-- PDF Documents Info -->
      <div class="bg-white border border-gray-300 rounded-lg shadow-sm p-6 mx-auto max-w-6xl">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold text-gray-800">PDF Document Loaded</h3>

          <!-- Toggle Button -->
          <button
            @click="showPdfDocument = !showPdfDocument"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <svg v-if="!showPdfDocument" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
            </svg>
            {{ showPdfDocument ? 'Hide Document' : 'View Document' }}
          </button>
        </div>

        <!-- PDF Viewer Component (Commented Out) -->
        <!-- <div v-if="pdfDocuments.pdf_data" class="mt-6">
          <PdfViewer :pdf-data="pdfDocuments.pdf_data" />
        </div> -->


  
        <!-- Option 3: Try both iframe and object approaches -->
        <div v-if="pdfDocuments.pdf_data && showPdfDocument" class="">
          <!-- Try object element first (might have better background control) -->
          <div class="pdf-container" style="background: white; padding: 0; border-radius: 8px; overflow: hidden;">
            <object
              :data="`data:application/pdf;base64,${pdfDocuments.pdf_data}#toolbar=0&navpanes=0&scrollbar=0&view=FitH`"
              type="application/pdf"
              width="100%"
              height="1200px"
              class="rounded-lg"
              style="background: white; display: block;"
            >
              <!-- Fallback to iframe if object doesn't work -->
              <iframe
                :src="`data:application/pdf;base64,${pdfDocuments.pdf_data}#toolbar=0&navpanes=0&scrollbar=0&view=FitH&zoom=page-width`"
                width="100%"
                height="700px"
                frameborder="0"
                class="rounded-lg"
                style="background: white; overflow: hidden;">
                <!-- Final fallback -->
                <p class="text-center text-gray-600 p-8">
                  Your browser doesn't support PDF viewing.
                  <a
                    :href="`data:application/pdf;base64,${pdfDocuments.pdf_data}`"
                    download="document.pdf"
                    class="text-blue-600 hover:text-blue-800 underline ml-2"
                  >
                    Download PDF instead
                  </a>
                </p>
              </iframe>
            </object>
          </div>



        </div>

        <!-- Message when PDF is hidden -->
        <div v-if="pdfDocuments.pdf_data && !showPdfDocument" class="mt-6 text-center text-gray-500 py-8">
          <p>PDF medical document is hidden. Click "View Document" to show it.</p>
        </div>

        <!-- Debug Info for API Response -->
        <!-- <div v-if="pdfDocuments" class="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 class="text-sm font-semibold text-gray-700 mb-2">API Response Debug:</h4>
          <div class="text-xs text-gray-600 space-y-1">
            <p><strong>PDF Data:</strong> {{ pdfDocuments.pdf_data ? 'Available' : 'Missing' }}</p>
            <p><strong>Template Token:</strong> {{ pdfDocuments.template_token || 'Missing' }}</p>
            <p><strong>Template URL:</strong> {{ pdfDocuments.template_url || 'Missing' }}</p>
            <p><strong>All Keys:</strong> {{ Object.keys(pdfDocuments).join(', ') }}</p>
          </div>
        </div> -->

        <!-- Documenso Signing Template -->
        <div v-if="pdfDocuments.template_token" class="mt-8">
          <h4 class="text-lg font-semibold text-gray-800 mb-4 text-center">Document Signing</h4>
          <DocumensoEmbed
            :token="pdfDocuments.template_token"
            :signerName="getPatientName()"
            :signerEmail="getPatientEmail()"
          />
        </div>

        <!-- No Template Token -->
        <div v-else-if="pdfDocuments && !pdfDocuments.template_token" class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
          <p class="text-yellow-700">No signing template available</p>
        </div>

        <!-- No PDF Data -->
        <div v-else class="mt-6 p-4 bg-yellow-50 border-2 border-yellow-300 rounded-lg text-center">
          <p class="text-yellow-700">No PDF data available to display</p>
        </div>
      </div>
    </div>

    <div class="py-4"></div>

    

  </div>

    <div class="py-4"></div>
    <div class="py-4"></div>


</template>

<script setup>
// Use patient layout (no header/navbar)
definePageMeta({
  layout: 'patient'
})

// Get the consent ID from the URL parameter
const route = useRoute()
const consentId = route.params.id

console.log('Patient consent third page loaded with consent ID:', consentId)

// State management
const pdfDocuments = ref(null)
const loading = ref(true)
const error = ref('')
const showPdfDocument = ref(false) // Toggle state for PDF document visibility

// Load PDF documents for signature
const loadPdfDocuments = async () => {
  try {
    loading.value = true
    error.value = ''

    const response = await $fetch(`/api/pdf-documents/${consentId}`)

    if (response && response.success) {
      pdfDocuments.value = response.data
      //console.log('PDF documents loaded:', pdfDocuments.value)

      // Log PDF data details
      if (pdfDocuments.value && pdfDocuments.value.pdf_data) {
        // console.log('PDF data available:', !!pdfDocuments.value.pdf_data)
        // console.log('PDF data type:', typeof pdfDocuments.value.pdf_data)
        // console.log('PDF data length:', pdfDocuments.value.pdf_data.length)
      }
    } else {
      console.log('No PDF documents found in response')
      error.value = 'No PDF documents found'
    }

  } catch (err) {
    console.error('Error loading PDF documents:', err)
    console.error('Error details:', err.data)
    error.value = err.data?.message || err.message || 'Failed to load PDF documents'
  } finally {
    loading.value = false
  }
}

// Helper functions to get patient data for Documenso
const getPatientName = () => {
  const name = pdfDocuments.value?.document?.Name || ''
  console.log('🏷️ Patient name for Documenso:', name)
  return name
}

const getPatientEmail = () => {
  const email = pdfDocuments.value?.document?.Email || ''
  console.log('📧 Patient email for Documenso:', email)
  return email
}

// Load PDF documents when component mounts
onMounted(async () => {
  if (consentId) {
    await loadPdfDocuments()
  } else {
    console.error('No consent ID provided')
    error.value = 'No consent ID provided'
    loading.value = false
  }
})

// Set page title
useHead({
  title: 'Patient Consent - Step 3 | Surgassists'
})
</script>

<style scoped>



</style>

