export default defineEventHandler(async (event) => {
  try {
    // Get the patient ID from the URL parameter
    const patientId = getRouterParam(event, 'id')
    
    if (!patientId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Patient ID is required'
      })
    }

    console.log('=== STARTING PATIENT SIGNATURE PROCESS ===')
    console.log('Patient ID:', patientId)

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()
    
    // Validate server configuration
    if (!config.goBackendApi || !config.apiKey) {
      console.error('Missing GO_BACKEND_API or API_KEY in environment variables')
      throw createError({
        statusCode: 500,
        statusMessage: 'Server configuration error'
      })
    }

    console.log('GO Backend URL:', config.goBackendApi + `/patient-started-signature/${patientId}`)

    // Call Go backend to start signature process
    const signatureResponse = await $fetch(`/patient-started-signature/${patientId}`, {
      baseURL: config.goBackendApi,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'X-API-Key': config.apiKey,
        'Content-Type': 'application/json'
      }
    })

    console.log('Go backend signature response:', signatureResponse)
    console.log('=== SIGNATURE PROCESS STARTED SUCCESSFULLY ===')

    // Return success response
    return {
      success: true,
      message: 'Signature process started successfully',
      data: signatureResponse
    }

  } catch (error) {
    console.error('=== GO BACKEND SIGNATURE ERROR ===')
    console.error('Error details:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data
    })

    // Handle specific GO backend HTTP status codes
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Patient record not found'
      })
    } else if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid patient ID'
      })
    } else if (error.statusCode === 401 || error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access denied'
      })
    } else if (error.statusCode >= 400 && error.statusCode < 500) {
      // Any other 4xx client error
      throw createError({
        statusCode: error.statusCode,
        statusMessage: 'Patient signature request failed'
      })
    } else if (error.statusCode >= 500) {
      // 5xx server errors
      throw createError({
        statusCode: 500,
        statusMessage: 'Server error occurred'
      })
    } else {
      // Network or other errors
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to start signature process'
      })
    }
  }
})
