export default defineEventHandler(async (event) => {
  try {
    const consentId = getRouterParam(event, 'id')
    
    console.log('=== MAKE QR CODE API ===')
    console.log('Consent ID:', consentId)
    
    if (!consentId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Consent ID is required'
      })
    }
    
    // Get runtime config for GO backend
    const config = useRuntimeConfig()
    const goBackendApi = config.goBackendApi
    const apiKey = config.apiKey
    
    console.log('Calling GO backend:', `${goBackendApi}/make-qr-code/${consentId}`)
    
    // Call GO backend API to generate QR code
    const response = await $fetch(`/make-qr-code/${consentId}`, {
      baseURL: goBackendApi,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      }
    })
    
    console.log('GO backend QR response:', response)
    
    return response
    
  } catch (error) {
    console.error('Error in make-qr-code API:', error)
    
    // Handle different error types
    if (error.response) {
      // GO backend returned an error
      throw createError({
        statusCode: error.response.status || 500,
        statusMessage: error.response.statusText || 'Backend API error'
      })
    } else if (error.statusCode) {
      // Re-throw existing HTTP errors
      throw error
    } else {
      // Generic server error
      throw createError({
        statusCode: 500,
        statusMessage: 'Internal server error'
      })
    }
  }
})
