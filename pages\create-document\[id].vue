<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
 
   <div class="mb-8">
      <div class="bg-gray-100 rounded-2xl p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Create Document</h1>
        <p v-if="patientId" class="mt-2 text-gray-600">Creating document for Patient ID: {{ patientId }}</p>
      </div>
    </div>  


    <!-- Page Header -->
    <div class="mb-8 flex justify-between items-start">

      <!-- Help Link -->
      <div>
        <NuxtLink
          :to="`/create-document-help/${patientId}`"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:text-blue-700 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Doewnload Forms, Help, Instructions
        </NuxtLink>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-2 text-gray-500">Loading patient information...</p>
    </div>

    <!-- Patient Info Card -->
    <div v-else-if="patient" class="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Patient Information</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">Name</label>
          <p class="mt-1 text-sm text-gray-900">{{ patient.Name }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Email</label>
          <p class="mt-1 text-sm text-gray-900">{{ patient.Email }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Hospital Number</label>
          <p class="mt-1 text-sm text-gray-900">{{ patient.HospitalNumber || 'N/A' }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Gender</label>
          <p class="mt-1 text-sm text-gray-900">{{ patient.MaleFemale || 'N/A' }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
          <p class="mt-1 text-sm text-gray-900">{{ formatDateOfBirth(patient.DOB) }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Added</label>
          <p class="mt-1 text-sm text-gray-900">{{ formatDate(patient.$createdAt) }}</p>
        </div>
      </div>
    </div>

    <!-- Document Upload Section -->
    <div v-if="patient" class="bg-white rounded-lg shadow-md p-6">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h2 class="text-lg font-semibold text-gray-900">Upload Document</h2>
        </div>
        <div class="flex items-center space-x-4">
          <label for="procedureSelect" class="text-sm font-medium text-gray-700">Procedure Type:</label>
          <select
            id="procedureSelect"
            v-model="selectedProcedure"
            class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            :disabled="proceduresLoading">
            <option value="">{{ proceduresLoading ? 'Loading...' : 'Select procedure' }}</option>
            <option
              v-for="procedure in procedures"
              :key="procedure.$id"
              :value="procedure.ProcedureType"
            >
              {{ procedure.ProcedureType }}
            </option>
          </select>
          <!-- Debug info -->
          <div v-if="procedures.length > 0" class="text-xs text-gray-500 mt-1">
            {{ procedures.length }} procedures loaded
          </div>
        </div>
      </div>
      <p class="text-gray-600 mb-6">Drag and drop a PDF document to upload for this patient. Only one PDF file is allowed.</p>

      <!-- Upload Success Message -->
      <div v-if="uploadSuccess" class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex">
          <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Document Uploaded Successfully!</h3>
            <p class="mt-2 text-sm text-green-700">
              File: <strong>{{ uploadedFile?.name }}</strong><br>
              Size: {{ formatFileSize(uploadedFile?.size) }}<br>
              Uploaded: {{ formatDate(new Date()) }}
            </p>
          </div>
        </div>
      </div>

      <!-- Drag and Drop Area -->
      <div v-if="!uploadSuccess" class="relative">
        <div
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragenter="handleDragEnter"
          @dragleave="handleDragLeave"
          :class="[
            'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
            isDragging ? 'border-blue-400 bg-blue-50' : 'border-gray-300',
            uploading ? 'opacity-50 pointer-events-none' : ''
          ]"
        >
          <!-- Upload Icon -->
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>

          <!-- Upload Text -->
          <div class="mt-4">
            <h3 class="text-lg font-medium text-gray-900">
              <span v-if="uploading">Uploading PDF...</span>
              <span v-else-if="isDragging">Drop PDF file here</span>
              <span v-else>Drag and drop your PDF document</span>
            </h3>
            <p class="mt-2 text-sm text-gray-500">
              <span v-if="uploading">Please wait while we upload your PDF file...</span>
              <span v-else>Or click to select a PDF file (PDF only)</span>
            </p>
          </div>

          <!-- File Input -->
          <input
            ref="fileInput"
            type="file"
            @change="handleFileSelect"
            accept=".pdf"
            class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            :disabled="uploading"
          />

          <!-- Upload Progress -->
          <div v-if="uploading" class="mt-4">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="{ width: uploadProgress + '%' }"></div>
            </div>
            <p class="mt-2 text-sm text-gray-600">{{ uploadProgress }}% uploaded</p>
          </div>
        </div>
      </div>

      <!-- Delete Document Button -->
      <div v-if="uploadSuccess" class="mt-4">
        <button
          @click="handleDeleteDocument"
          class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition-colors inline-flex items-center"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Delete Doc
        </button>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6">
      <div class="flex">
        <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error Loading Patient</h3>
          <p class="mt-2 text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'


definePageMeta({
  middleware: 'auth'
})


const route = useRoute()
const patientId = route.params.id
const { isLoggedIn, currentUser, checkAuth } = useAuth()
const { logAction } = useLogger()

// State management
const patient = ref(null)
const loading = ref(true)
const error = ref('')

// Upload state
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadSuccess = ref(false)
const uploadedFile = ref(null)
const isDragging = ref(false)

// Procedures dropdown state
const procedures = ref([])
const selectedProcedure = ref('')
const proceduresLoading = ref(false)



// File input reference
const fileInput = ref(null)

// Load procedures from Go backend
const loadProcedures = async () => {
  try {
    proceduresLoading.value = true
    console.log('Loading procedures from server API...')

    // Call our server-side API endpoint
    const response = await $fetch('/api/procedures')

 

    if (response && response.success && response.procedures) {
      procedures.value = response.procedures
  
    } else {
      console.log('No procedures found in response')
     // console.log('Response structure:', JSON.stringify(response, null, 2))
    }

  } catch (err) {
    console.error('Error loading procedures:', err)
    console.error('Error details:', err.message)
    // Don't show error to user - procedures dropdown is not critical
  } finally {
    proceduresLoading.value = false
  }
}

// Load patient data
const loadPatient = async () => {
  if (!patientId) {
    error.value = 'Patient ID is required'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = ''

    // Import Appwrite service
    const { appwriteService } = await import('~/services/appwrite')

    // Get patient by ID
    const patientData = await appwriteService.getPatientById(patientId)

    if (!patientData) {
      error.value = 'Patient not found'
      return
    }

    patient.value = patientData
    console.log('Patient loaded:', patientData)

  } catch (err) {
    console.error('Error loading patient:', err)
    error.value = err.message || 'Failed to load patient information'
  } finally {
    loading.value = false
  }
}

// Format date helper functions
const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatDateOfBirth = (dateString) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString)
  return date.toLocaleDateString('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatFileSize = (bytes) => {
  if (!bytes) return 'N/A'
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// Drag and Drop handlers
const handleDragEnter = (e) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragOver = (e) => {
  e.preventDefault()
}

const handleDragLeave = (e) => {
  e.preventDefault()
  isDragging.value = false
}

const handleDrop = (e) => {
  e.preventDefault()
  isDragging.value = false

  const files = e.dataTransfer.files
  if (files.length > 0) {
    handleFileUpload(files[0])
  }
}

// File selection handler
const handleFileSelect = (e) => {
  const files = e.target.files
  if (files.length > 0) {
    handleFileUpload(files[0])
  }
}

// File upload function using Go Backend API
const handleFileUpload = async (file) => {
  if (!file) return

  // Validate procedure type is selected
  if (!selectedProcedure.value) {
    alert('Please select a procedure type before uploading the document.')
    return
  }

  // Validate file type - ONLY PDF allowed
  if (file.type !== 'application/pdf') {
    alert('Please select a PDF file only. Other file types are not allowed.')
    return
  }

  // Validate file size (max 10MB)
  if (file.size > 10 * 1024 * 1024) {
    alert('File size must be less than 10MB')
    return
  }

  try {
    uploading.value = true
    uploadProgress.value = 0

    console.log('=== UPLOADING FILE VIA GO BACKEND ===')
    console.log('File:', file.name, file.type, file.size)
    console.log('UserID:', currentUser.value.$id)
    console.log('PatientUserID:', patientId)
    console.log('ProcedureType:', selectedProcedure.value)

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 20
      }
    }, 200)

    // Create FormData for multipart/form-data
    const formData = new FormData()
    formData.append('UserID', currentUser.value.$id)
    formData.append('PatientUserID', patientId)
    formData.append('ProcedureType', selectedProcedure.value)
    formData.append('file', file)

    console.log('FormData created, calling Go backend...')

    // Call server-side API that will forward to Go backend
    const response = await $fetch('/api/upload-document', {
      method: 'POST',
      body: formData
    })

    // Complete progress
    clearInterval(progressInterval)
    uploadProgress.value = 100

 

    if (response.success) {
      // Extract document ID from Go backend response (it's in 'file_id')
      const documentId = response.file_id

      console.log('Extracted file_id:', documentId)

      if (!documentId) {
        console.error('No file_id found in Go backend response!')
        console.error('Full response:', JSON.stringify(response, null, 2))
        throw new Error('No file_id returned from server')
      }

      // Set uploaded file info using Go backend response fields
      uploadedFile.value = {
        $id: documentId,                    // file_id from Go backend
        name: response.filename || file.name,  // filename from Go backend or original
        size: response.size || file.size,      // size from Go backend or original
        type: file.type,                       // keep original file type
        url: response.file_url || response.url // file URL if provided
      }

      console.log('uploadedFile.value set to:', uploadedFile.value)

      // Set success state to show the delete button
      uploadSuccess.value = true

      // Log the upload action (keep under 40 chars)
      if (currentUser.value) {
        await logAction(currentUser.value.$id, 'Uploaded document')
      }

    } else {
      throw new Error(response.message || 'Upload failed')
    }

  } catch (err) {
    console.error('Error uploading file via Go backend:', err)
    console.error('Error details:', err.data)
    alert(`Failed to upload document: ${err.data?.message || err.message || 'Unknown error'}. Please try again.`)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}


// Delete uploaded document
const handleDeleteDocument = async () => {
  if (!uploadedFile.value) {
    console.error('No uploaded file to delete')
    return
  }


  if (!uploadedFile.value.$id) {
    console.error('No document ID found in uploadedFile!')
    alert('Cannot delete document: No document ID found. Please refresh the page and try again.')
    return
  }

  // Confirm deletion
  const confirmed = confirm(`Are you sure you want to delete "${uploadedFile.value.name}"? This action cannot be undone.`)
  if (!confirmed) return

  try {

    const response = await $fetch(`/api/documents/${uploadedFile.value.$id}`, {
      method: 'DELETE'
    })

    console.log('Server-side delete response:', response)

    // Log the delete action (keep under 40 chars)
    if (currentUser.value) {
      await logAction(currentUser.value.$id, 'Deleted document')
    }

    // Reset upload state
    resetUpload()

    console.log('Document deleted successfully via server-side API')

  } catch (err) {
    console.error('Error deleting document:', err)
    console.error('Error details:', err.data)
    alert(`Failed to delete document: ${err.data?.message || err.message || 'Unknown error'}. Please try again.`)
  }
}

// Reset upload state
const resetUpload = () => {
  uploadSuccess.value = false
  uploadedFile.value = null
  uploadProgress.value = 0
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// Check authentication and load data when component mounts
onMounted(async () => {
  await checkAuth()

  if (isLoggedIn.value && currentUser.value) {
    // Log the page view (keep under 40 chars)
    await logAction(currentUser.value.$id, 'Viewed create document page')

    // Load patient data and procedures in parallel
    await Promise.all([
      loadPatient(),
      loadProcedures()
    ])
  } else {
    console.log('User not authenticated')
    error.value = 'Authentication required'
  }
})

// Set page title
useHead({
  title: 'Create Document | Surgassists'
})
</script>