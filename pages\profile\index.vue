<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Loading state -->
    <div v-if="isLoading" class="text-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-4 text-gray-600">Loading profile...</p>
    </div>

    <!-- Not logged in -->
    <div v-else-if="!isLoggedIn" class="text-center">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Access Denied</h1>
      <p class="text-gray-600 mb-8">You need to be logged in to access your profile.</p>
      <NuxtLink
        to="/login"
        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
      >
        Go to Login
      </NuxtLink>
    </div>

    <!-- Profile content -->
    <div v-else>
     

      <!-- Profile Header Card -->
      <div class="mb-8">
        <div class="rounded-xl p-8 text-white text-center shadow-lg" style="background-color: #65ADEE;">
          <!-- Profile Image -->
          <div class="mb-6">
            <div v-if="userProfile?.ProfileImage" class="inline-block">
              <img
                :src="getProfileImageSrc(userProfile.ProfileImage)"
                alt="Profile Image"
                class="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg mx-auto"
              />
            </div>
            <div v-else class="inline-block">
              <div class="w-24 h-24 rounded-full bg-white/20 border-4 border-white flex items-center justify-center mx-auto">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
            </div>

            <!-- Edit Photo Button -->
            <div class="mt-3">
              <button
                @click="openImageUpload"
                class="text-sm text-white/90 hover:text-white font-medium underline"
              >
                {{ userProfile?.ProfileImage ? 'Edit Photo' : 'Add Photo' }}
              </button>
            </div>
          </div>

          <!-- User Name and Title -->
          <div>
            <h1 class="text-2xl font-bold mb-2">
              {{ userProfile?.Title || '' }}
              {{ userProfile?.ConsultantName || currentUser?.name || 'Consultant Name' }}
            </h1>
            <p class="text-lg opacity-90">
              {{ userProfile?.Department || 'Department' }}
            </p>
          </div>
        </div>
      </div>


      <!-- Profile not found -->
      <div v-if="!profileLoading && !userProfile" class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Profile Not Found</h3>
            <p class="mt-1 text-sm text-yellow-700">
              You don't have a profile set up yet. Click the button below to create your consultant profile.
            </p>
            <div class="mt-4">
              <button
                @click="handleCreateClick"
                class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Create Profile
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile exists -->
      <div v-else-if="userProfile" class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">Consultant Information</h2>
            <button
              @click="handleEditClick"
              class="text-white px-4 py-2 rounded-md text-sm font-medium transition-colors hover:opacity-90"
              style="background-color: #65ADEE;">
              Edit Profile
            </button>
          </div>
        </div>

        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">Title</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile?.Title || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Consultant Name</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile?.ConsultantName || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Email to Receive Documents</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile.Email || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Phone</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile.Phone || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Department</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile.Department || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Available</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile.Available || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Department Telephone</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile.Telephone || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Address Line 1</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile.Address1 || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Address Line 2</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile.Address2 || 'Not provided' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Post Code</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ userProfile.PostCode || 'Not provided' }}</dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Create/Edit Profile Form Modal -->
      <div v-if="showCreateForm || showEditForm" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              {{ showCreateForm ? 'Create Profile' : 'Edit Profile' }}
            </h3>

            <form @submit.prevent="handleSubmit" class="space-y-4">
              <!-- Title Dropdown -->
              <div>
                <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
                <select
                  id="title"
                  v-model="formData.Title"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Title</option>
                  <option value="Dr">Dr</option>
                  <option value="Mr">Mr</option>
                  <option value="Mrs">Mrs</option>
                  <option value="Miss">Miss</option>
                  <option value="Ms">Ms</option>
                  <option value="Prof">Prof</option>
                  <option value="Professor">Professor</option>
                </select>
              </div>

              <div>
                <label for="consultantName" class="block text-sm font-medium text-gray-700">Consultant Name</label>
                <input
                  id="consultantName"
                  v-model="formData.ConsultantName"
                  type="text"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email to Receive Documents</label>
                <input
                  id="email"
                  v-model="formData.Email"
                  type="email"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                <input
                  id="phone"
                  v-model="formData.Phone"
                  type="tel"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label for="department" class="block text-sm font-medium text-gray-700">Department</label>
                <input
                  id="department"
                  v-model="formData.Department"
                  type="text"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label for="available" class="block text-sm font-medium text-gray-700">Available</label>
                <input
                  id="available"
                  v-model="formData.Available"
                  type="text"
                  required
                  placeholder="e.g., Monday-Friday 9AM-5PM"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label for="address1" class="block text-sm font-medium text-gray-700">Address Line 1</label>
                <input
                  id="address1"
                  v-model="formData.Address1"
                  type="text"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label for="address2" class="block text-sm font-medium text-gray-700">Address Line 2</label>
                <input
                  id="address2"
                  v-model="formData.Address2"
                  type="text"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label for="postCode" class="block text-sm font-medium text-gray-700">Post Code</label>
                <input
                  id="postCode"
                  v-model="formData.PostCode"
                  type="text"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label for="telephone" class="block text-sm font-medium text-gray-700">Telephone</label>
                <input
                  id="telephone"
                  v-model="formData.Telephone"
                  type="tel"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Error message -->
              <div v-if="error" class="text-red-500 text-sm">
                {{ error }}
              </div>

              <!-- Form buttons -->
              <div class="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  @click="closeForm"
                  class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  :disabled="formLoading"
                  class="px-4 py-2 text-white rounded-md text-sm font-medium transition-colors disabled:opacity-50 hover:opacity-90"
                  style="background-color: #65ADEE;"
                >
                  <span v-if="formLoading">{{ showCreateForm ? 'Creating...' : 'Updating...' }}</span>
                  <span v-else>{{ showCreateForm ? 'Create Profile' : 'Update Profile' }}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Upload Modal -->
    <div v-if="showImageUpload" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Upload Profile Image</h3>

          <!-- File Upload -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Select Image
            </label>
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              @change="handleFileSelect"
              class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>

          <!-- Preview -->
          <div v-if="selectedFile" class="mb-4 text-center">
            <img
              :src="imagePreview"
              alt="Preview"
              class="w-24 h-24 rounded-full object-cover border-2 border-gray-300 mx-auto"
            />
            <p class="text-sm text-gray-500 mt-2">Preview</p>
          </div>

          <!-- Error Message -->
          <div v-if="imageUploadError" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p class="text-sm text-red-700">{{ imageUploadError }}</p>
          </div>

          <!-- Buttons -->
          <div class="flex justify-end space-x-3">
            <button
              @click="closeImageUpload"
              :disabled="imageUploading"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              @click="uploadImage"
              :disabled="!selectedFile || imageUploading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <span v-if="imageUploading">Uploading...</span>
              <span v-else>Upload</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'

// Use auth composable
const { isLoggedIn, currentUser, isLoading, checkAuth } = useAuth()

// Helper function to get profile image source
const getProfileImageSrc = (profileImage) => {
  if (!profileImage) return ''

  // If it's already a data URL (base64), return as is
  if (profileImage.startsWith('data:image/')) {
    return profileImage
  }

  // If it's a URL, return as is (fallback for existing data)
  if (profileImage.startsWith('http')) {
    return profileImage
  }

  // If it's just base64 data without the data URL prefix, add it
  if (profileImage.length > 100 && !profileImage.includes('http')) {
    return `data:image/jpeg;base64,${profileImage}`
  }

  return profileImage
}

// Profile state
const userProfile = ref(null)
const profileLoading = ref(false)
const showCreateForm = ref(false)
const showEditForm = ref(false)
const formLoading = ref(false)
const error = ref('')

// Image upload state
const showImageUpload = ref(false)
const selectedFile = ref(null)
const imagePreview = ref('')
const imageUploading = ref(false)
const imageUploadError = ref('')
const fileInput = ref(null)

// Form data
const formData = reactive({
  Title: '',
  ConsultantName: '',
  Email: '',
  Phone: '',
  Department: '',
  Available: '',
  Address1: '',
  Address2: '',
  PostCode: '',
  Telephone: '',
  UserID: ''
})

// Load user profile
const loadUserProfile = async () => {
  if (!currentUser.value) return

  try {
    profileLoading.value = true

    // Use server-side API to get profile from Go backend
    const response = await $fetch(`/api/get-profile-consultant/${currentUser.value.$id}`)


    if (response.success && response.data) {
      // Extract the profile data from the nested response
      userProfile.value = response.data.profile || response.data
      console.log('User profile loaded from Go backend:', userProfile.value)
      console.log('Profile ConsultantName:', userProfile.value.ConsultantName)
      console.log('Profile ConsultantName type:', typeof userProfile.value.ConsultantName)
      console.log('Profile ConsultantName length:', userProfile.value.ConsultantName?.length)
      console.log('All profile keys:', Object.keys(userProfile.value))

      // Test reactivity
      setTimeout(() => {
        console.log('After timeout - userProfile.value:', userProfile.value)
        console.log('After timeout - ConsultantName:', userProfile.value?.ConsultantName)
      }, 100)

      // Initialize form data with loaded profile data
      initializeFormData()
    } else {
      console.log('No profile found, user needs to create one')
      userProfile.value = null

      // Initialize empty form data for create mode
      initializeFormData()
    }
  } catch (err) {
    console.error('Error loading profile:', err)
    // If profile doesn't exist, that's okay - user can create one
    if (err.status === 404) {
      console.log('Profile not found - user needs to create one')
      userProfile.value = null
    }
  } finally {
    profileLoading.value = false
  }
}

// Initialize form data
const initializeFormData = () => {
  if (userProfile.value) {
    // Edit mode - populate with existing data
    formData.Title = userProfile.value.Title || ''
    formData.ConsultantName = userProfile.value.ConsultantName || ''
    formData.Email = userProfile.value.Email || ''
    formData.Phone = userProfile.value.Phone || ''
    formData.Department = userProfile.value.Department || ''
    formData.Available = userProfile.value.Available || ''
    formData.Address1 = userProfile.value.Address1 || ''
    formData.Address2 = userProfile.value.Address2 || ''
    formData.PostCode = userProfile.value.PostCode || ''
    formData.Telephone = userProfile.value.Telephone || ''
    formData.UserID = userProfile.value.UserID || currentUser.value.$id
  } else {
    // Create mode - clear form
    formData.Title = ''
    formData.ConsultantName = ''
    formData.Email = ''
    formData.Phone = ''
    formData.Department = ''
    formData.Available = ''
    formData.Address1 = ''
    formData.Address2 = ''
    formData.PostCode = ''
    formData.Telephone = ''
    formData.UserID = currentUser.value.$id
  }
}

// Handle form submission
const handleSubmit = async () => {
  try {
    formLoading.value = true
    error.value = ''

    console.log('=== FORM SUBMISSION DEBUG ===')
    console.log('Form data being sent:', formData)
    console.log('Title value:', formData.Title)
    console.log('Title type:', typeof formData.Title)

    // Use server-side API for both create and update
    const response = await $fetch('/api/update-profile-consultant', {
      method: 'PUT',
      body: formData
    })

    console.log('Profile operation completed:', response)
    console.log(`Profile ${response.action}: ${response.message}`)

    // Reload profile data
    await loadUserProfile()

    // Close form
    closeForm()
  } catch (err) {
    console.error('Error saving profile:', err)

    // Show only user-friendly messages - NO technical details
    let userMessage = 'Unable to save your profile. Please try again.'

    // Only use server message if it's clean and user-friendly
    if (err.statusMessage &&
        !err.statusMessage.includes('api/') &&
        !err.statusMessage.includes('fetch') &&
        !err.statusMessage.includes('500') &&
        !err.statusMessage.includes('PUT') &&
        !err.statusMessage.includes('GET') &&
        !err.statusMessage.includes('[') &&
        !err.statusMessage.includes('Failed to')) {
      userMessage = err.statusMessage
    }

    error.value = userMessage
  } finally {
    formLoading.value = false
  }
}

// Close form
const closeForm = () => {
  showCreateForm.value = false
  showEditForm.value = false
  error.value = ''
}

// Handle profile image upload
const openImageUpload = () => {
  showImageUpload.value = true
  imageUploadError.value = ''
}

const closeImageUpload = () => {
  showImageUpload.value = false
  selectedFile.value = null
  imagePreview.value = ''
  imageUploadError.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // Validate file type
  if (!file.type.startsWith('image/')) {
    imageUploadError.value = 'Please select a valid image file.'
    return
  }

  // Validate file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    imageUploadError.value = 'Image size must be less than 5MB.'
    return
  }

  selectedFile.value = file
  imageUploadError.value = ''

  // Create preview
  const reader = new FileReader()
  reader.onload = (e) => {
    imagePreview.value = e.target.result
  }
  reader.readAsDataURL(file)
}

const uploadImage = async () => {
  if (!selectedFile.value || !currentUser.value) return

  try {
    imageUploading.value = true
    imageUploadError.value = ''

    // Create FormData for file upload
    const formData = new FormData()
    formData.append('image', selectedFile.value)

    console.log('Uploading image for user:', currentUser.value.$id)

    // Call server-side API to upload image
    const response = await $fetch(`/api/update-profile-image-consultant/${currentUser.value.$id}`, {
      method: 'PUT',
      body: formData
    })

    console.log('Image upload response:', response)

    if (response.success) {
      // Close modal
      closeImageUpload()

      // Refresh profile data to get updated ProfileImage
      await loadUserProfile()

      console.log('Profile image updated successfully!')
    }

  } catch (err) {
    console.error('Error uploading image:', err)

    // Show user-friendly error message
    let errorMessage = 'Unable to upload image. Please try again.'

    if (err.statusMessage &&
        !err.statusMessage.includes('api/') &&
        !err.statusMessage.includes('fetch')) {
      errorMessage = err.statusMessage
    }

    imageUploadError.value = errorMessage
  } finally {
    imageUploading.value = false
  }
}

// Watch for edit form opening
const openEditForm = () => {
  initializeFormData()
  showEditForm.value = true
}

// Watch for create form opening
const openCreateForm = () => {
  initializeFormData()
  showCreateForm.value = true
}

// Check authentication and load profile when component mounts
onMounted(async () => {
  await checkAuth()
  if (isLoggedIn.value && currentUser.value) {
    await loadUserProfile()
  }
})

// Update the template to use the new functions
const handleEditClick = () => {
  openEditForm()
}

const handleCreateClick = () => {
  openCreateForm()
}
</script>
