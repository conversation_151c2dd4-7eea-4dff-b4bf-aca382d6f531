export default defineEventHandler(async (event) => {
  try {
    console.log('=== UPLOADING DOCUMENT VIA GO BACKEND ===')

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    console.log('Go Backend API URL:', config.goBackendApi)
    console.log('API Key exists:', !!config.apiKey)

    // Read the multipart form data from the request
    const formData = await readMultipartFormData(event)

    if (!formData) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No form data received'
      })
    }

    console.log('Form data received, fields:', formData.length)

    // Extract form fields
    let userID, patientUserID, procedureType, fileData, fileName, fileType

    for (const field of formData) {
      console.log('Processing field:', field.name, field.filename ? `(file: ${field.filename})` : `(value: ${field.data?.toString()})`)
      
      if (field.name === 'UserID') {
        userID = field.data.toString()
      } else if (field.name === 'PatientUserID') {
        patientUserID = field.data.toString()
      } else if (field.name === 'ProcedureType') {
        procedureType = field.data.toString()
      } else if (field.name === 'file') {
        fileData = field.data
        fileName = field.filename
        fileType = field.type
      }
    }

    // Validate required fields
    if (!userID || !patientUserID || !procedureType || !fileData) {
      console.log('Missing required fields:', { userID: !!userID, patientUserID: !!patientUserID, procedureType: !!procedureType, fileData: !!fileData })
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: UserID, PatientUserID, ProcedureType, and file are required'
      })
    }

    console.log('Form data extracted:')
    console.log('- UserID:', userID)
    console.log('- PatientUserID:', patientUserID)
    console.log('- ProcedureType:', procedureType)
    console.log('- File:', fileName, fileType, fileData.length, 'bytes')

    // Create new FormData for Go backend
    const goFormData = new FormData()
    goFormData.append('UserID', userID)
    goFormData.append('PatientUserID', patientUserID)
    goFormData.append('ProcedureType', procedureType)
    
    // Create a Blob from the file data and append it
    const fileBlob = new Blob([fileData], { type: fileType })
    goFormData.append('file', fileBlob, fileName)
    
    // Forward to Go backend
    const response = await $fetch(`${config.goBackendApi}/upload-document-processing`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`
        // Don't set Content-Type - let fetch set it with boundary for multipart
      },
      body: goFormData
    })

    console.log('Go backend response:', response)
    console.log('Response type:', typeof response)

    // Return success response mapped from Go backend fields
    return {
      success: true,
      message: response.message || 'Document uploaded successfully',
      file_id: response.file_id,           // Primary field from Go backend
      document_id: response.file_id,       // Alternative mapping
      documentId: response.file_id,        // Alternative mapping
      user_id: response.user_id,
      patient_user_id: response.patient_user_id,
      procedure_type: response.procedure_type,
      filename: response.filename,
      size: response.size,
      file_url: response.file_url,
      url: response.file_url,              // Alternative field name
      raw_response: response               // For debugging
    }

  } catch (error) {
    console.error('Error uploading document via Go backend:', error)
    console.error('Error status:', error.status)
    console.error('Error message:', error.message)
    console.error('Error data:', error.data)
    
    // Handle specific Go backend errors
    if (error.data && error.data.error) {
      throw createError({
        statusCode: error.status || 500,
        statusMessage: error.data.error
      })
    } else {
      throw createError({
        statusCode: error.status || error.statusCode || 500,
        statusMessage: error.message || 'Failed to upload document'
      })
    }
  }
})
