<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Loading state -->
    <div v-if="loading" class="text-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-4 text-gray-600">Loading patient consent...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="text-center">
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
      <h1 class="mt-4 text-3xl font-bold text-gray-900">Consent Record Not Found</h1>
      <p class="mt-2 text-gray-600">{{ error }}</p>
      <div class="mt-6">
        <NuxtLink
          to="/"
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
        >
          Go to Home
        </NuxtLink>
      </div>
    </div>

    <!-- Consent record found -->
    <div v-else-if="consentRecord">
      <!-- Page Header -->
      <div class="mb-8 text-center">
        <h1 class="text-4xl font-bold text-gray-900">Patient Information</h1>
        <p class="mt-2 text-lg text-gray-600">Please watch the video and proceed with signing</p>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

        <!-- Video Player Section -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Information Video</h2>
            <p class="text-sm text-gray-600">Please watch this important information</p>
          </div>
          <div class="p-6">
            <div class="relative bg-black rounded-lg overflow-hidden">
              <video
                ref="videoPlayer"
                class="w-full h-auto"
                controls
                controlsList="nodownload"
                preload="metadata"
                allowfullscreen
                webkit-playsinline
                playsinline
                @loadedmetadata="onVideoLoaded"
                @error="onVideoError"
                @fullscreenchange="onFullscreenChange"
              >
                <source src="/videos/temp.mp4" type="video/mp4">
                <p class="text-white p-4">
                  Your browser does not support the video tag.
                  <a href="/videos/temp.mp4" class="text-blue-400 underline">Download the video</a>
                </p>
              </video>
            </div>

            <!-- Video Controls Info -->
            <div class="mt-4 text-sm text-gray-500">
              <p>💡 Use the controls to play, pause, and adjust volume</p>
            </div>
          </div>
        </div>

        <!-- Signing Card Section -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Next Steps</h2>
            <p class="text-sm text-gray-600">Complete your consent process</p>
          </div>
          <div class="p-6">

            <!-- Ready to Sign Card -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900">Ready to sign the docs</h3>
                  <p class="text-sm text-gray-600 mt-1">After watching the video, you can proceed with the digital signature</p>
                </div>
              </div>
            </div>

            <!-- Patient Information -->
            <div class="space-y-4">
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 mb-3">Your Information</h4>
                <dl class="space-y-2">
                  <div class="flex justify-between">
                    <dt class="text-sm text-gray-600">Email:</dt>
                    <dd class="text-sm font-medium text-gray-900">{{ consentRecord.Email }}</dd>
                  </div>
                  <div class="flex justify-between">
                    <dt class="text-sm text-gray-600">Status:</dt>
                    <dd>
                      <span :class="getStatusColor(consentRecord.Status)" class="px-2 py-1 text-xs font-medium rounded-full">
                        {{ consentRecord.Status }}
                      </span>
                    </dd>
                  </div>
                  <div class="flex justify-between">
                    <dt class="text-sm text-gray-600">Date:</dt>
                    <dd class="text-sm font-medium text-gray-900">{{ formatDate(consentRecord.$createdAt) }}</dd>
                  </div>
                </dl>
              </div>

              <!-- Sign Consent Button -->
              <div class="mt-6">
                <button
                  @click="handleSignConsent"
                  :disabled="isSigningInProgress"
                  :class="[
                    'w-full font-semibold py-3 px-6 rounded-lg transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2',
                    isSigningInProgress
                      ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 text-white transform hover:scale-105 focus:ring-blue-500'
                  ]"
                >
                  <div class="flex items-center justify-center">
                    <!-- Loading spinner when signing in progress -->
                    <svg
                      v-if="isSigningInProgress"
                      class="animate-spin h-5 w-5 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <!-- Pen icon when not signing -->
                    <svg
                      v-else
                      class="h-5 w-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                    </svg>
                    {{ isSigningInProgress ? 'Processing...' : 'Sign Consent' }}
                  </div>
                </button>
              </div>

              <!-- Placeholder for 3rd party integration -->
              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4">
                <div class="flex items-center">
                  <svg class="h-5 w-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <p class="text-sm text-yellow-800">
                    <strong>Digital Signature:</strong>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

// Get the route to access the ID parameter
const route = useRoute()
const patientId = route.params.id

// State
const consentRecord = ref(null)
const loading = ref(true)
const error = ref('')

// Dynamic status steps - will be populated from backend data
const statusSteps = ref([])

// Default status workflow (fallback if backend doesn't provide workflow)
const defaultStatusSteps = [
  'Started',
  'Email Sent',
  'Patient Open Form',
  'Patient Consent Done',
  'VR App Viewed'
]

// Load consent record by ID using server-side API
const loadConsentRecord = async () => {
  try {
    loading.value = true
    error.value = ''

    console.log('Loading consent record for ID:', patientId)

    // Use server-side API to fetch consent data securely
    const response = await $fetch(`/api/patient/${patientId}`)

    if (response.success && response.data) {
      consentRecord.value = response.data
      console.log('Consent record loaded:', response.data)

      // Create dynamic status workflow based on current status
      createStatusWorkflow(response.data.Status)
    } else {
      error.value = `No consent record found with ID: ${patientId}`
    }

  } catch (err) {
    console.error('Error loading consent record:', err)

    // Handle specific error codes
    if (err.statusCode === 404) {
      error.value = `No consent record found with ID: ${patientId}`
    } else {
      error.value = 'Failed to load consent record. Please check the ID and try again.'
    }
  } finally {
    loading.value = false
  }
}

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Create dynamic status workflow based on current status
const createStatusWorkflow = (currentStatus) => {
  // Use default workflow but ensure current status is included
  const workflow = [...defaultStatusSteps]

  // If current status is not in default workflow, add it at appropriate position
  if (!workflow.includes(currentStatus)) {
    // Add the current status to the workflow
    workflow.push(currentStatus)
  }

  statusSteps.value = workflow
  console.log('Created status workflow:', workflow)
  console.log('Current status:', currentStatus)
}

// Get status color for display (dynamic colors)
const getStatusColor = (status) => {
  // Define colors for known statuses
  const statusColors = {
    'Started': 'bg-blue-100 text-blue-800',
    'Email Sent': 'bg-indigo-100 text-indigo-800',
    'Patient Open Form': 'bg-yellow-100 text-yellow-800',
    'Patient Consent Done': 'bg-green-100 text-green-800',
    'VR App Viewed': 'bg-purple-100 text-purple-800'
  }

  // Return specific color if known, otherwise use a dynamic color
  return statusColors[status] || 'bg-emerald-100 text-emerald-800'
}

// Video player reference and handlers
const videoPlayer = ref(null)

// Button state management
const isSigningInProgress = ref(false)

// Handle video loaded
const onVideoLoaded = () => {
  console.log('Video loaded successfully')
}

// Handle video error
const onVideoError = (event) => {
  console.error('Video failed to load:', event)
}

// Handle fullscreen changes
const onFullscreenChange = () => {
  const isFullscreen = document.fullscreenElement === videoPlayer.value
  console.log('Fullscreen mode:', isFullscreen ? 'enabled' : 'disabled')
}

// Handle sign consent button click
const handleSignConsent = async () => {
  // Prevent multiple clicks
  if (isSigningInProgress.value) {
    console.log('Sign consent already in progress, ignoring click')
    return
  }

  try {
    // Set button to loading state
    isSigningInProgress.value = true

    // Call Go backend to start signature process
    console.log('Calling Go backend to start signature process...')

    // TODO: Temporarily commented out - no need to send email notification
    // const response = await $fetch(`/api/patient-signature/${patientId}`, {
    //   method: 'GET'
    // })
    //
    // console.log('Go backend response:', response)

    // Show success message
    alert('Your documents are ready to sign and will you be redirected to the signing page.')

    // Keep button disabled after successful submission
    console.log('Sign consent process completed successfully - button remains disabled')

    // Wait 2 seconds then redirect to document signing
    console.log('Waiting 2 seconds before redirecting to document signing...')
    setTimeout(() => {
      console.log('Redirecting to document signing URL')
      // Redirect to external document signing URL
      window.location.href = 'https://app.documenso.com/d/kzhLXrNuJdArrw9HmltTJ'
    }, 2000)

  } catch (error) {
    console.error('Error starting signature process:', error)

    // Re-enable button on error so user can retry
    isSigningInProgress.value = false

    // Show error message
    if (error.statusCode === 404) {
      alert('Patient record not found. Please check the link and try again.')
    } else {
      alert('Unable to start signature process. Please try again later.')
    }
  }
}

// Load consent record when component mounts
onMounted(() => {
  if (patientId) {
    loadConsentRecord()
  } else {
    error.value = 'No patient ID provided in URL'
    loading.value = false
  }
})

// Set page title
useHead({
  title: `Patient Consent - ${patientId} | Surgassists`
})
</script>
