# Appwrite Usage Documentation

This document outlines all the places in the Surgassists AR Portal codebase that use Appwrite client for document and storage operations (excluding authentication operations like login/logout).

## 🗄️ **Appwrite Database & Storage Operations**

### **1. Services (`services/appwrite.js`)**

The main Appwrite service file contains all database and storage operations:

#### **Profile Operations:**
- ✅ `createUserProfile(profileData)` - Create new user profile
- ✅ `updateUserProfile(documentId, profileData)` - Update existing user profile

#### **Logging Operations:**
- ✅ `createLog(userId, logAction)` - Create activity log entry
- ✅ `createLogWithIP(userId, logAction, ipAddress)` - Create log with IP tracking
- ✅ `getUserLogs(userId, limit)` - Retrieve user activity logs

#### **Consent Operations:**
- ✅ `createConsent(userId, email, name, procedure, status, documentIdSent)` - Create patient consent record
- ✅ `getUserConsents(userId)` - Get all consents for a user
- ✅ `getConsentById(consentId)` - Get specific consent by ID

#### **Reports Operations:**
- ✅ `getUserReports(userId)` - Get reports for a user
- ✅ `getReportsAnalytics(userId)` - Get reports with consent cross-referencing

#### **Patients Operations:**
- ✅ `createPatient(name, email, hospitalNumber, maleFemale, dob, docURL, loginUserId)` - Create new patient record
- ✅ `getPatients()` - Get all patients
- ✅ `getPatientById(patientId)` - Get specific patient by ID
- ✅ `deletePatient(patientId)` - Delete patient record

#### **Storage Operations:**
- ✅ `uploadDocument(file, patientId)` - Upload document to Appwrite Storage
- ✅ `getDocumentUrl(fileId)` - Get document download/view URL
- ✅ `deleteDocument(fileId)` - Delete document from storage

#### **DocumentsUpload Operations:**
- ✅ `createDocumentUploadRecord(userId, patientUserId, documentId, procedureType)` - Track document uploads
- ✅ `removeDocumentFromUploadRecord(userId, patientUserId, documentId)` - Remove document tracking

---

### **2. Pages Using Appwrite Services**

#### **`/patients-consent/index.vue`**
**Purpose:** Patient consent management dashboard
**Appwrite Operations:**
- ✅ `appwriteService.getPatients()` - Load patients list for dropdown selection
- ✅ `appwriteService.getUserConsents(currentUser.$id)` - Load consent records for current user

#### **`/reports/index.vue`**
**Purpose:** Reports and analytics dashboard
**Appwrite Operations:**
- ✅ `appwriteService.getReportsAnalytics(currentUser.$id)` - Load reports with consent cross-referencing

#### **`/patient-list-add/index.vue`**
**Purpose:** Patient management (add, view, delete patients)
**Appwrite Operations:**
- ✅ `appwriteService.createPatient(...)` - Add new patient to database
- ✅ `appwriteService.getPatients()` - Load all patients for display
- ✅ `appwriteService.deletePatient(patientId)` - Delete patient from database

---

### **3. Server-Side Appwrite Operations**

#### **`/server/api/patient-info/[id].get.js`**
**Purpose:** Server-side patient information retrieval for thank you pages
**Appwrite Operations:**
- ✅ `databases.getDocument()` - Get patient by ID from Patients collection
- ✅ `databases.getDocument()` - Get consultant profile from Profile collection

**Note:** Uses server-side Appwrite client with API key for secure access.

---

## 📊 **Appwrite Collections Used**

| Collection | ID | Purpose | Operations |
|------------|----|---------|-----------| 
| **Profile** | `6852be26001922ea0c85` | User profiles | Create, Update, Read |
| **Log** | `6852d0fa00256ad3188b` | Activity logging | Create, Read |
| **Consent** | `6853de08003879050263` | Patient consents | Create, Read, List |
| **Reports** | `68540de400285bfcb5b6` | VR app reports | Read, List, Analytics |
| **Patients** | `appwritePatientsCollectionId` | Patient records | Create, Read, List, Delete |
| **DocumentsUpload** | `688b5c0c00381f295042` | Document tracking | Create, Read, Delete |

## 🗂️ **Storage Buckets Used**

| Bucket | Purpose | Operations |
|--------|---------|------------|
| **Document Storage** | `appwriteDocumentStorageBucketId` | Patient document files | Upload, Download, Delete |

---

## 🔐 **Authentication Requirements**

**All client-side Appwrite operations require user authentication:**
- ✅ User must be logged in to access database operations
- ✅ Operations are filtered by `UserID` for data security
- ✅ Server-side operations use API key for secure access

**Pages requiring authentication:**
- `/patients-consent` - Protected by auth middleware
- `/reports` - Protected by auth middleware  
- `/patient-list-add` - Protected by auth middleware

**Public pages (no auth required):**
- Patient consent pages (`/patient-consent-*`) - Use server-side APIs instead

---

## 🚀 **Usage Pattern**

```javascript
// Typical usage pattern in Vue pages
const { appwriteService } = await import('~/services/appwrite')
const result = await appwriteService.methodName(parameters)
```

**Example:**
```javascript
// Load user's consent records
const { appwriteService } = await import('~/services/appwrite')
const consents = await appwriteService.getUserConsents(currentUser.value.$id)
```

---

## ⚠️ **Important Notes**

1. **Client-side operations** require user authentication
2. **Server-side operations** use API key for secure access
3. **Data filtering** by UserID ensures data security
4. **Error handling** is implemented for all operations
5. **Logging operations** don't throw errors to prevent app breakage
