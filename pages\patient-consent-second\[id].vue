<template>

 <!-- <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> -->
<div>
    <!-- Top Image -->
    <div class="w-full" style="background-color: #EFF9FC;">
      <div class="mb-8 text-center">
       <img src="/images/TopImage.png" alt="Surgassists" class="mx-auto max-w-full h-auto" />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-16">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-lg text-gray-600">Loading patient information...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center py-16">
      <div class="text-center max-w-2xl mx-auto">
        <div class="bg-red-50 border border-red-200 rounded-lg p-8">
          <svg class="mx-auto h-16 w-16 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <h2 class="text-2xl font-bold text-red-800 mb-4">Information Not Available</h2>
          <!-- <p class="text-lg text-red-700 mb-6">{{ getErrorMessage() }}</p> -->
          <div class="space-y-2 text-sm text-red-600">
            <p>This could happen if:</p>
            <ul class="list-disc list-inside space-y-1">
              <li>The patient consent record doesn't exist</li>
              <li>The link has expired or is invalid</li>
              <li>The patient information is not available</li>
            </ul>
          </div>
          <div class="mt-6">
            <p class="text-sm text-red-600">
              If you believe this is an error, please contact your healthcare provider.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content (only show if no error and not loading) -->
    <div v-else-if="consentRecord">

      <!-- Header -->
      <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8"
        style="height: 150px;">
            <div class="text-center">
                <p class="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
                    Patient Information
                </p>
                <p class="text-lg sm:text-xl lg:text-2xl text-gray-600">
                   Please watch the informational video for
    instructions on how to consent

                </p>
            </div>
        </div>
 
<div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
  <div class="flex items-center justify-center gap-3">
    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
      <span class="text-white font-bold text-lg">1</span>
    </div>
    <p class="text-lg sm:text-xl lg:text-1xl text-gray-600">
      Watch Informational Video
    </p>
  </div>
</div>
    
<div class="py-4"></div>


<div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
  <div class="flex items-center justify-center gap-3">
    
    <!-- White card with grey border -->
    <div class="bg-white border border-gray-300 rounded-lg shadow-sm p-6 max-w-2xl mx-auto">
      <p class="text-gray-700 leading-relaxed">
        <span class="font-semibold text-1xl">Informational Video</span>
        <br>
        Please watch for instructions on how to use the Mixed Reality
      </p>

      <div class="py-4"></div>

      <!-- Video Player Section -->


        <!-- Video Section -->
        <div class="mx-auto">
          <div class="relative bg-black rounded-lg overflow-hidden">
            <video
              class="w-full h-auto"
              controls
              controlsList="nodownload"
              preload="metadata"
              allowfullscreen
              webkit-playsinline
              playsinline
            >
              <source src="/videos/temp.mp4" type="video/mp4">
              <p class="text-white p-4">
                Your browser does not support the video tag.
                <a href="/videos/temp.mp4" class="text-blue-400 underline">Download the video</a>
              </p>
            </video>
          </div>
        </div>


        <div class="py-4"></div>

        <div class="flex items-center gap-3 max-w-4xl mx-auto">
            <svg class="w-5 h-5 text-yellow-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            <span class="text-sm text-gray-600">
                Use controls to play, pause, and adjust volume
            </span>
        </div>

    </div>

    

  </div>
</div>

<div class="py-4"></div>



 <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

<div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
  <div class="flex items-center justify-center gap-3">
     <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
      <span class="text-white font-bold text-lg">2</span>
    </div>
    <p class="text-lg sm:text-base lg:text-1xl text-gray-600">
     If this email is on your desktop scan below QR Code via your phone
    </p>
   
  </div>  
</div>


    <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
            <p class="text-lg sm:text-base lg:text-1xl text-gray-600 mb-6">
      Follow the link if you have accessed this email via your phone
    </p>    
    </div>


    <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <!-- QR Code Display -->
          <div v-if="qrCodeLoading" class="p-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p class="text-gray-600 mt-4">Generating QR Code...</p>
          </div>

          <div v-else-if="qrCodeError" class="p-8">
            <p class="text-red-600">{{ qrCodeError }}</p>
          </div>

          <div v-else-if="qrCodeData" class="p-8">
            <img
              :src="qrCodeData"
              alt="QR Code for Patient Consent"
              class="mx-auto max-w-xs w-full h-auto"
            />
          </div>

          <div v-else class="p-8">
            <p class="text-gray-600">QR Code not available</p>
          </div>
        </div>
    </div>

</div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

    <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
  <div class="flex items-center justify-center gap-3">
     <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
      <span class="text-white font-bold text-lg">3</span>
    </div>
    <p class="text-lg sm:text-base lg:text-1xl text-gray-600">
        After watching the video, you can proceed with the digital signature
    </p>
   
  </div>




</div>

</div>

    <!-- Ready to sign card -->
    <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="px-4 sm:px-6 lg:px-8">
          <!-- Ready to sign card -->
          <div class="max-w-7xl rounded-xl shadow-lg p-8 sm:p-10 lg:p-12" style="background-color: #E6F1F6;">
            <div class="flex-1">

              <p class="text-1xl sm:text-1xl font-bold text-gray-800">
                Ready To Sign The Docs
              </p>

              <p class="text-lg text-gray-700 leading-relaxed">
                After watching the video, you can proceed with<br>
                the digital signature
              </p>

            </div>
          </div>
        </div>
    </div>
     



        <!-- Digital Signature Button -->
    <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8 py-6">
      <NuxtLink
        :to="`/patient-consent-third/${consentId}`"
        class="inline-flex items-center px-8 py-3 text-lg font-semibold text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
        style="background-color: #65ADEE;" >
        Digital Signature
      </NuxtLink>
    </div>


    <div class="py-4"></div>

    <div class="py-4"></div>


</div>

    </div> <!-- End of main content div -->

</template>

<script setup>
import { ref, onMounted } from 'vue'

// Use patient layout (no header/navbar)
definePageMeta({
  layout: 'patient'
})

// Get the consent ID from the route
const route = useRoute()
const consentId = route.params.id

// Main state management
const consentRecord = ref(null)
const loading = ref(true)
const error = ref('')

// QR Code state management
const qrCodeData = ref(null)
const qrCodeLoading = ref(false)
const qrCodeError = ref('')

// Load consent data from API
const loadConsentData = async () => {
  try {
    loading.value = true
    error.value = ''

    console.log('=== LOADING CONSENT DATA ===')
    console.log('Consent ID:', consentId)

    // Call API to get consent data (same as patient-consent-first)
    const response = await $fetch(`/api/patient/${consentId}`)

    console.log('API Response:', response)

    // Extract consent record from response
    let consentData = null
    if (response && response.document) {
      consentData = response.document
    } else if (response && response.data) {
      consentData = response.data
    } else if (response && typeof response === 'object' && response.$id) {
      consentData = response
    } else {
      throw new Error('Consent record not found')
    }

    consentRecord.value = consentData
    console.log('Consent data loaded:', consentData)

  } catch (err) {
    console.error('Error loading consent data:', err)

    // Check for specific error types from Go backend
    if (err.data && err.data.error) {
      if (err.data.error === 'Patient document not found') {
        error.value = 'PATIENT_NOT_FOUND'
      } else {
        error.value = err.data.error
      }
    } else {
      error.value = err.message || 'Failed to load consent data'
    }
  } finally {
    loading.value = false
  }
}

// Fetch QR code from API
const fetchQRCode = async () => {
  try {
    qrCodeLoading.value = true
    qrCodeError.value = ''

    console.log('=== FETCHING QR CODE ===')
    console.log('Consent ID:', consentId)

    // Call API to generate QR code
    const response = await $fetch(`/api/make-qr-code/${consentId}`)

    console.log('QR Code API Response:', response)
    console.log('Response type:', typeof response)
    console.log('Response keys:', response ? Object.keys(response) : 'No response')

    // Check different possible response structures
    if (response && response.qr_code_base64) {
      console.log('Found QR code in response.qr_code_base64')
      qrCodeData.value = `data:image/png;base64,${response.qr_code_base64}`
    } else if (response && response.qrCode) {
      console.log('Found QR code in response.qrCode')
      qrCodeData.value = response.qrCode
    } else if (response && response.data) {
      console.log('Found QR code in response.data')
      qrCodeData.value = response.data
    } else if (response && response.qr_code) {
      console.log('Found QR code in response.qr_code')
      qrCodeData.value = response.qr_code
    } else if (response && response.QRCode) {
      console.log('Found QR code in response.QRCode')
      qrCodeData.value = response.QRCode
    } else if (response && response.document && response.document.QRCode) {
      console.log('Found QR code in response.document.QRCode')
      qrCodeData.value = response.document.QRCode
    } else if (typeof response === 'string') {
      console.log('Response is a string, using directly')
      qrCodeData.value = response
    } else {
      console.log('Could not find QR code in any expected location')
      console.log('Full response structure:', JSON.stringify(response, null, 2))
      throw new Error('QR code data not found in response')
    }

    console.log('QR Code loaded successfully')

  } catch (err) {
    console.error('Error fetching QR code:', err)
    qrCodeError.value = err.message || 'Failed to generate QR code'
  } finally {
    qrCodeLoading.value = false
  }
}

// Get user-friendly error message
const getErrorMessage = () => {
  switch (error.value) {
    case 'PATIENT_NOT_FOUND':
      return 'The patient consent record could not be found. The consent link may be invalid or expired.'
    case 'Patient document not found':
      return 'The patient consent record could not be found. The consent link may be invalid or expired.'
    case 'No consent ID provided':
      return 'No consent ID was provided in the link. Please check the link and try again.'
    default:
      return error.value || 'An unexpected error occurred while loading the patient information.'
  }
}

// Load consent data and QR code when component mounts
onMounted(async () => {
  if (consentId) {
    // Load consent data first
    await loadConsentData()

    // Only load QR code if consent data loaded successfully
    if (consentRecord.value) {
      await fetchQRCode()
    }
  } else {
    console.error('No consent ID provided')
    error.value = 'No consent ID provided'
    loading.value = false
  }
})

// Set page title
useHead({
  title: 'Patient Consent - Step 2 | Surgassists'
})
</script>