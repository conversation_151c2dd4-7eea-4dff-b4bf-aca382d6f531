export default defineEventHandler(async (event) => {
  try {
    // Get the document ID from the URL parameter
    const documentId = getRouterParam(event, 'id')

    if (!documentId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Document ID is required'
      })
    }

    console.log('=== DELETING DOCUMENT VIA GO BACKEND ===')
    console.log('Document ID:', documentId)

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    console.log('Go Backend API URL:', config.goBackendApi)
    console.log('API Key exists:', !!config.apiKey)
    console.log('Full URL:', `${config.goBackendApi}/delete-document/${documentId}`)

    // Call Go backend to delete document
    const response = await $fetch(`${config.goBackendApi}/delete-document/${documentId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })


    console.log('Go backend response:', response)

    // Check if deletion was successful
    if (response && response.message === 'Document deleted successfully') {
      console.log('Document deleted successfully via Go backend')
      return {
        success: true,
        message: response.message,
        documentId: response.document_id || documentId
      }
    } else if (response && response.error) {
      console.log('Go backend returned error:', response.error)
      throw createError({
        statusCode: 404,
        statusMessage: response.error
      })
    } else {
      console.log('Unexpected response from Go backend')
      throw createError({
        statusCode: 500,
        statusMessage: 'Unexpected response from backend'
      })
    }

  } catch (error) {
    console.error('Error deleting document via Go backend:', error)
    console.error('Error status:', error.status)
    console.error('Error message:', error.message)
    console.error('Error data:', error.data)

    // Handle specific Go backend errors
    if (error.data && error.data.error) {
      if (error.data.error === 'Document not found in collection') {
        throw createError({
          statusCode: 404,
          statusMessage: 'Document not found'
        })
      } else {
        throw createError({
          statusCode: error.status || 500,
          statusMessage: error.data.error
        })
      }
    } else {
      throw createError({
        statusCode: error.status || error.statusCode || 500,
        statusMessage: error.message || 'Failed to delete document'
      })
    }
  }
})
