export default defineEventHandler(async (event) => {
  try {
    const userId = getRouterParam(event, 'id')
    
    console.log('=== ADMIN GET USER INFO API ===')
    console.log('Requested User ID:', userId)
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }
    
    // Get runtime config for GO backend
    const config = useRuntimeConfig()
    const goBackendApi = config.goBackendApi
    const apiKey = config.apiKey
    
    console.log('Calling GO backend:', `${goBackendApi}/admin-get-user-info/${userId}`)
    
    // Call GO backend API
    const response = await $fetch(`/admin-get-user-info/${userId}`, {
      baseURL: goBackendApi,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      }
    })
    
    console.log('GO backend response:', response)
    
    return response
    
  } catch (error) {
    console.error('Error in admin-get-user-info API:', error)
    
    // Handle different error types
    if (error.response) {
      // GO backend returned an error
      throw createError({
        statusCode: error.response.status || 500,
        statusMessage: error.response.statusText || 'Backend API error'
      })
    } else if (error.statusCode) {
      // Re-throw existing HTTP errors
      throw error
    } else {
      // Generic server error
      throw createError({
        statusCode: 500,
        statusMessage: 'Internal server error'
      })
    }
  }
})
