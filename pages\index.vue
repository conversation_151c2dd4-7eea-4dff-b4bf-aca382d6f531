<template>
  <!-- Loading state while checking authentication -->
  <div class="min-h-screen flex items-center justify-center">
    <div class="text-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p class="text-gray-600">Loading...</p>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// Use auth composable
const { isLoggedIn, checkAuth } = useAuth()

// Smart redirection on page load
onMounted(async () => {
  // Check authentication status
  await checkAuth()

  // Redirect based on authentication status
  if (isLoggedIn.value) {
    // User is logged in - redirect to dashboard
    await navigateTo('/dashboard')
  } else {
    // User is not logged in - redirect to login
    await navigateTo('/login')
  }
})

// Set page title
useHead({
  title: 'Surgassists'
})
</script>