export default defineEventHandler(async (event) => {
  try {
    console.log('=== FETCHING PROCEDURES FROM GO BACKEND ===')

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    console.log('Go Backend API URL:', config.goBackendApi)
    console.log('API Key exists:', !!config.apiKey)
    console.log('Full URL:', `${config.goBackendApi}/procedures`)

    // Make request to Go backend with proper Bearer Token authentication
    const response = await $fetch(`${config.goBackendApi}/procedures`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    console.log('Go backend response:', response)
    console.log('Response type:', typeof response)
    console.log('Response keys:', response ? Object.keys(response) : 'No response')

    // Return the procedures data
    return {
      success: true,
      procedures: response.data?.documents || [],
      total: response.data?.total || 0,
      raw_response: response // For debugging
    }

  } catch (error) {
    console.error('Error fetching procedures:', error)
    console.error('Error status:', error.status)
    console.error('Error message:', error.message)
    console.error('Error data:', error.data)

    throw createError({
      statusCode: error.status || error.statusCode || 500,
      statusMessage: error.message || 'Failed to fetch procedures'
    })
  }
})
