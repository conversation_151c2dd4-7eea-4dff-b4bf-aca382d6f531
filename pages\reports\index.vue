<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

    <div class="mb-8">
      <div class="rounded-2xl p-8 text-white" style="background-color: #65ADEE;">
        <h1 class="text-3xl font-bold mb-4">Reports & Analytics</h1>
        <p class="text-lg text-white/90 mb-6">View detailed analytics, generate comprehensive reports, and track patient outcomes</p>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-4">
          <button class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export Data
          </button>
          <button class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Generate Report
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-4 text-gray-600">Loading reports...</p>
    </div>

    <!-- Reports Dashboard -->
    <div v-else>
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Consents -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Consents</p>
              <p class="text-2xl font-semibold text-gray-900">{{ analytics.totalConsents }}</p>
            </div>
          </div>
        </div>

        <!-- Total Reports -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Reports</p>
              <p class="text-2xl font-semibold text-gray-900">{{ analytics.totalReports }}</p>
            </div>
          </div>
        </div>

        <!-- Completed Consents -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Completed</p>
              <p class="text-2xl font-semibold text-gray-900">{{ analytics.completedConsents }}</p>
            </div>
          </div>
        </div>

        <!-- Pending Consents -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Pending</p>
              <p class="text-2xl font-semibold text-gray-900">{{ analytics.pendingConsents }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Data Tables -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
        <!-- Recent Consents -->
        <div class="bg-white rounded-lg shadow-md">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Consents</h3>
          </div>
          <div class="p-6">
            <div v-if="recentConsents.length > 0" class="space-y-4">
              <div
                v-for="consent in recentConsents"
                :key="consent.$id"
                class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
              >
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ consent.Email }}</p>
                  <p class="text-xs text-gray-500">{{ formatDate(consent.$createdAt) }}</p>
                  <p class="text-xs text-gray-400">{{ consent.Procedure || 'No procedure' }}</p>
                </div>
                <span :class="getStatusColor(consent.Status)" class="px-2 py-1 text-xs font-medium rounded-full">
                  {{ consent.Status }}
                </span>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">No consent records found</p>
            </div>
          </div>
        </div>

        <!-- Recent Reports -->
        <div class="bg-white rounded-lg shadow-md">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Reports</h3>
          </div>
          <div class="p-6">
            <div v-if="recentReports.length > 0" class="space-y-4">
              <div
                v-for="report in recentReports"
                :key="report.$id"
                class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
              >
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ report.Title || 'Report' }}</p>
                  <p class="text-xs text-gray-500">{{ formatDate(report.$createdAt) }}</p>
                </div>
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                  Report
                </span>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">No reports found</p>
          
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Summary -->
      <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Data Summary</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <p class="text-2xl font-bold text-blue-600">{{ analytics.totalConsents }}</p>
            <p class="text-sm text-gray-500">Total Consent Records</p>
          
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-green-600">{{ analytics.totalReports }}</p>
            <p class="text-sm text-gray-500">Total Report Records</p>
      
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-purple-600">{{ Math.round((analytics.completedConsents / analytics.totalConsents) * 100) || 0 }}%</p>
            <p class="text-sm text-gray-500">Completion Rate</p>
         
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAuth } from '~/composables/useAuth'
import { useLogger } from '~/composables/useLogger'

// Protect this page - require authentication
definePageMeta({
  middleware: 'auth'
})

// Use auth composable
const { isLoggedIn, currentUser, checkAuth } = useAuth()
const { logAction, LOG_ACTIONS } = useLogger()

// State management
const loading = ref(true)
const reportsData = ref({ reports: [], consents: [] })

// Load reports and analytics data
const loadReportsData = async () => {
  if (!currentUser.value) return

  try {
    loading.value = true
    const { appwriteService } = await import('~/services/appwrite')

    // Get both reports and consents data for cross-referencing
    const data = await appwriteService.getReportsAnalytics(currentUser.value.$id)
    reportsData.value = data

    console.log('Reports data loaded:', data)
  } catch (error) {
    console.error('Error loading reports data:', error)
  } finally {
    loading.value = false
  }
}

// Computed analytics
const analytics = computed(() => {
  const { reports, consents } = reportsData.value

  // Basic counts
  const totalReports = reports.length
  const totalConsents = consents.length

  // Status analysis
  const statusCounts = {}
  let completedConsents = 0
  let pendingConsents = 0

  consents.forEach(consent => {
    const status = consent.Status || 'Unknown'
    statusCounts[status] = (statusCounts[status] || 0) + 1

    // Count completed vs pending
    if (status === 'VR App Viewed' || status === 'Patient Consent Done') {
      completedConsents++
    } else {
      pendingConsents++
    }
  })

  // Status distribution for charts
  const statusDistribution = Object.entries(statusCounts).map(([status, count]) => ({
    status,
    count,
    percentage: totalConsents > 0 ? Math.round((count / totalConsents) * 100) : 0
  }))

  // Monthly trends (simplified for now)
  const monthlyTrends = getMonthlyTrends(consents)

  return {
    totalReports,
    totalConsents,
    completedConsents,
    pendingConsents,
    statusDistribution,
    monthlyTrends
  }
})

// Recent data for tables
const recentConsents = computed(() => {
  return reportsData.value.consents.slice(0, 5)
})

const recentReports = computed(() => {
  return reportsData.value.reports.slice(0, 5)
})

// Helper functions
const getMonthlyTrends = (consents) => {
  const monthlyData = {}

  consents.forEach(consent => {
    const date = new Date(consent.$createdAt)
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1
  })

  return Object.entries(monthlyData)
    .sort(([a], [b]) => a.localeCompare(b))
    .slice(-6) // Last 6 months
    .map(([month, count]) => ({
      month: new Date(month + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      count
    }))
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now - date) / (1000 * 60))
    return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes} minutes ago`
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
    } else {
      return date.toLocaleDateString()
    }
  }
}

const getStatusColor = (status) => {
  switch (status) {
    case 'Started': return 'bg-blue-100 text-blue-800'
    case 'Patient Open Form': return 'bg-yellow-100 text-yellow-800'
    case 'Patient Consent Done': return 'bg-green-100 text-green-800'
    case 'QR Generated': return 'bg-indigo-100 text-indigo-800'
    case 'VR App Viewed': return 'bg-purple-100 text-purple-800'
    case 'Email Sent': return 'bg-orange-100 text-orange-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

// Check authentication status when component mounts
onMounted(async () => {
  await checkAuth()
  if (isLoggedIn.value && currentUser.value) {
    await logAction(currentUser.value.$id, LOG_ACTIONS.REPORTS_VIEWED)
    await loadReportsData()
  }
})
</script>
