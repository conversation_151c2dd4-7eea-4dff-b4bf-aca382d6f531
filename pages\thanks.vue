<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <!-- Surgassists Logo/Branding -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Surgassists</h1>
        <p class="text-sm text-gray-600">AR Portal</p>
      </div>

      <!-- Thank You Card -->
      <div class="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
        <div class="text-center">
          <!-- Success Icon -->
          <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>

          <!-- Thank You Message -->
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Thank You!</h2>

          <div class="space-y-4 text-gray-600">
            <p class="text-lg">
              Your documents have been signed successfully.
            </p>

            <p>
              Your consent process is now complete.
            </p>

            <p class="text-sm">
              Thank you for using Surgassists.
            </p>
          </div>

          <!-- Additional Information -->
          <div class="mt-8 p-4 bg-green-50 rounded-lg">
            <div class="flex items-start">
              <svg class="h-5 w-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <div class="text-left">
                <h3 class="text-sm font-medium text-green-900 mb-1">Process Complete</h3>
                <ul class="text-sm text-green-800 space-y-1">
                  <li>• Your digital signature has been recorded</li>
                  <li>• All consent documentation is complete</li>
                  <li>• You may now close this window</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="mt-6 pt-6 border-t border-gray-200">
            <p class="text-sm text-gray-500">
              Thank you for choosing Surgassists for your medical care.
            </p>
            <div class="mt-2">
              <a
                href="https://surgassists-connect.online"
                class="text-blue-600 hover:text-blue-500 text-sm font-medium"
              >
                Visit Surgassists Portal
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="mt-8 text-center">
        <p class="text-xs text-gray-500">
          © {{ new Date().getFullYear() }} Surgassists. All rights reserved.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
// Set page meta for public access (no authentication required)
definePageMeta({
  auth: false
})

// Set page title
useHead({
  title: 'Thank You - Surgassists',
  meta: [
    {
      name: 'description',
      content: 'Thank you for completing your consent process with Surgassists.'
    }
  ]
})

// Log page visit
onMounted(() => {
  console.log('=== THANKS PAGE LOADED ===')
  console.log('Timestamp:', new Date().toISOString())
  console.log('User completed consent process successfully')
})
</script>




