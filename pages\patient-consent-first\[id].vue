<template>

 <div class="">

    <!-- Top Image -->
       <div class="w-full" style="background-color: #EFF9FC;">
    <div class="text-center">
      <img src="/images/TopImage.png" alt="Surgassists" class="mx-auto max-w-full h-auto" />
    </div>
       </div>
 

  

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-16">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-lg text-gray-600">Loading consent information...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center py-16">
      <div class="text-center max-w-2xl mx-auto">
        <div class="bg-red-50 border border-red-200 rounded-lg p-8">
          <svg class="mx-auto h-16 w-16 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <h2 class="text-2xl font-bold text-red-800 mb-4">Information Not Available</h2>
        <!--  <p class="text-lg text-red-700 mb-6">{{ getErrorMessage() }}</p> -->
          <div class="space-y-2 text-sm text-red-600">
            <p>This could happen if:</p>
            <ul class="list-disc list-inside space-y-1">
              <li>The consent record doesn't exist</li>
              <li>The link has expired or is invalid</li>
              <li>The patient information is not available</li>
            </ul>
          </div>
          <div class="mt-6">
            <p class="text-sm text-red-600">
              If you believe this is an error, please contact your healthcare provider.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content (only show if no error and not loading) -->
    <div v-else-if="consentRecord">

        <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16">
        <div class="text-center max-w-2xl mx-auto">
            <p class="text-2xl sm:text-2xl lg:text-2xl font-bold text-gray-800 mb-6">
              {{ getGreeting() }}, {{ getPatientName() }}
            </p>
            <p class="text-lg sm:text-xl lg:text-2xl text-gray-600 leading-relaxed">
              {{ getConsultantName() }} has requested your consent for<br>
              an upcoming medical procedure. We're<br>
              using mixed reality technology to help you<br>
              better understand your treatment in a clear,<br>
              visual way.
            </p>
        </div>
    </div>

       <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16"
       style="background-color: #EFF9FC; height: 150px;">
         <div class="text-center max-w-2xl mx-auto">
                 <p class="text-2xl sm:text-2xl lg:text-2xl font-bold text-gray-800 mb-6">
                    Ready to give your consent?
                 </p>
         <NuxtLink
           :to="`/patient-consent-second/${consentId}`"
           class="inline-flex items-center px-8 py-3 text-lg font-semibold text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
           style="background-color: #65ADEE;"
         >
           Start Consent Now
         </NuxtLink>
         </div>
        </div>


    <!-- Add space at the top -->
    <div class="py-6 sm:py-6 lg:py-6"></div>

    <div class="flex items-center justify-center">
        <div class="px-4 sm:px-6 lg:px-8">
          <!-- Green card -->
          <div class="max-w-7xl rounded-xl shadow-lg p-8 sm:p-10 lg:p-12" style="background-color: #E9F8EE;">
            <div class="flex flex-col lg:flex-row items-center gap-8">

            <!-- Content Section -->
            <div class="flex-1 text-center">
              <!-- Image and Title Row -->
              <div class="flex items-center gap-4 mb-6">
                <img
                  src="/images/small-website.webp"
                  alt="Mixed Reality Medical Procedure"
                  style="height: 40px; width: 20px;"
                />
                <h2 class="text-xl sm:text-2xl font-bold" style="color: #359549;">
                  Why MR Consent?
                </h2>
              </div>

              <div class="space-y-4">
                <!-- Checkmark Items -->
                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center" style="background-color: #359549;">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <p class="text-gray-700 text-sm sm:text-lg">Watch a 3D animation of your exact procedure</p>
                </div>

                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center" style="background-color: #359549;">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <p class="text-gray-700 text-sm sm:text-lg">Understand what will happen step-by-step</p>
                </div>

                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center" style="background-color: #359549;">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <p class="text-gray-700 text-sm sm:text-lg">Available in your preferred language</p>
                </div>

                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center" style="background-color: #359549;">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <p class="text-gray-700 text-sm sm:text-lg">Feel more confident about your treatment</p>
                </div>

                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center" style="background-color: #359549;">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <p class="text-gray-700 text-sm sm:text-lg">Only takes 3-5 minutes to complete</p>
                </div>
              </div>
            </div>

            </div>
          </div>
        </div>
    </div>

     <!-- Add space at the top -->
    <div class="py-6 sm:py-6 lg:py-6"></div>

    <div class="flex items-center justify-center">
        <div class="px-4 sm:px-6 lg:px-8">
          <!-- Golden security card -->
          <div class="rounded-xl shadow-lg p-12 sm:p-12 lg:p-14" style="background-color: #FDFBE9;">
            <div class="flex-1 text-center">

              <!-- Golden lock icon and title -->
              <div class="flex items-center justify-center gap-4 mb-6">
                <div class="flex-shrink-0">
                  <svg class="w-8 h-8" style="color: #D4AF37;" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zM12 7c1.1 0 2 .9 2 2v2h-4V9c0-1.1.9-2 2-2zm3 6v5H9v-5h6z"/>
                  </svg>
                </div>
                <h2 class="text-2xl sm:text-2xl font-bold text-gray-800">
                  Need Help or Have Questions?
                </h2>
              </div>

              <p class="text-lg text-gray-700 leading-relaxed">
                Your personal health information is<br>
                protected and encrypted
              </p>

            </div>
          </div>
        </div>
    </div>


    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="flex px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16">
          
                
          <p class="text-lg sm:text-xl lg:text-2xl text-gray-600 leading-relaxed">
            <span class="text-2xl font-bold"> Help or Have Questions?</span><br>
            Our team is here to support you. If you have any questions about your procedure or need technical
assistance, please don't hesitate to contact us:
        </p>
        </div>
    </div>

  

    <div class="flex items-center justify-center">
        <div class="px-4 sm:px-6 lg:px-8">
          <!-- Consultant contact info card -->
          <div class="max-w-7xl rounded-xl shadow-lg p-8 sm:p-10 lg:p-12" style="background-color: #EFF9FC;">
            <div class="flex-1 text-center">

              <h2 class="text-2xl sm:text-3xl font-bold text-gray-800 mb-8">
                Contact Information
              </h2>

              <!-- Contact details list -->
              <div class="space-y-4 max-w-2xl mx-auto">

                <!-- Phone -->
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                  </svg>
                  <span class="text-sm md:text-lg  text-gray-800">Phone: {{ getConsultantPhone() }}</span>
                </div>

                <!-- Email -->
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  <span class="text-sm md:text-lg text-gray-800">Email: {{ getConsultantEmail() }}</span>
                </div>

                <!-- Department -->
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  <span class="text-sm md:text-lg text-gray-800">Department: {{ getConsultantDepartment() }}</span>
                </div>

                <!-- Available -->
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm md:text-lg text-gray-800">Available: {{ getConsultantAvailable() }}</span>
                </div>

              </div>

            </div>
          </div>
        </div>
    </div>


    
    </div> <!-- End of main content div -->

    

 </div>

<div class="py-8"></div>
<div class="py-8"></div>
<div class="py-8"></div>



</template>

<script setup>
import { ref, onMounted } from 'vue'

// Use patient layout (no header/navbar)
definePageMeta({
  layout: 'patient'
})

// Get the consent ID from the URL parameter
const route = useRoute()
const consentId = route.params.id

// State management
const consentRecord = ref(null)
const consultantProfile = ref(null)
const loading = ref(false)
const consultantLoading = ref(false)
const error = ref('')

// Fetch consent data from API (same pattern as /patient/[id].vue)
const fetchConsentData = async () => {
  try {
    loading.value = true
    error.value = ''

    console.log('=== FETCHING CONSENT DATA ===')
    console.log('Consent ID:', consentId)

    // Use server-side API to fetch consent data securely
    const response = await $fetch(`/api/patient/${consentId}`)

    console.log('API Response:', response)
    console.log('Response type:', typeof response)
    console.log('Response keys:', response ? Object.keys(response) : 'No response')

    // Check different possible response structures
    if (response && response.document) {
      // GO backend returns nested under 'document'
      consentRecord.value = response.document
      console.log('Consent record loaded from response.document:', consentRecord.value)
    } else if (response && response.data) {
      // Maybe it's under 'data'
      consentRecord.value = response.data
      console.log('Consent record loaded from response.data:', consentRecord.value)
    } else if (response && typeof response === 'object' && response.$id) {
      // Maybe the response IS the consent record directly
      consentRecord.value = response
      console.log('Consent record loaded directly from response:', consentRecord.value)
    } else {
      console.log('Could not find consent data in response structure')
      throw new Error('Consent record not found')
    }

    // After consent data is loaded, fetch consultant profile using UserID
    if (consentRecord.value && consentRecord.value.UserID) {
      await fetchConsultantProfile(consentRecord.value.UserID)
    }

  } catch (err) {
    console.error('Error fetching consent data:', err)

    // Check for specific error types from Go backend
    if (err.data && err.data.error) {
      if (err.data.error === 'Patient document not found') {
        error.value = 'PATIENT_NOT_FOUND'
      } else if (err.data.error === 'User profile not found') {
        error.value = 'PROFILE_NOT_FOUND'
      } else {
        error.value = err.data.error
      }
    } else {
      error.value = err.message || 'Failed to load consent data'
    }
  } finally {
    loading.value = false
  }
}

// Fetch consultant profile data using UserID from consent record
const fetchConsultantProfile = async (userId) => {
  try {
    consultantLoading.value = true

    console.log('=== FETCHING CONSULTANT PROFILE ===')
    console.log('Consultant UserID:', userId)

    // Call API to get consultant profile
    const response = await $fetch(`/api/admin-get-user-info/${userId}`)

    console.log('Consultant API Response:', response)

    // Store consultant profile data
    consultantProfile.value = response
    console.log('Consultant profile loaded:', consultantProfile.value)

  } catch (err) {
    console.error('Error fetching consultant profile:', err)
    // Don't set error for consultant - it's not critical for page display
  } finally {
    consultantLoading.value = false
  }
}

// Load consent data when component mounts
onMounted(async () => {
  console.log('Patient consent first page loaded')

  if (consentId) {
    await fetchConsentData()
  } else {
    console.error('No consent ID provided')
    error.value = 'No consent ID provided'
    loading.value = false
  }
})

// Helper functions for personalized content
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) {
    return 'Good Morning'
  } else if (hour < 17) {
    return 'Good Afternoon'
  } else {
    return 'Good Evening'
  }
}

const getPatientName = () => {
  if (consentRecord.value && consentRecord.value.Name) {
    return `Mr ${consentRecord.value.Name}`
  }
  return 'Mr Patient' // Fallback if name not loaded yet
}

const getConsultantName = () => {
  console.log('=== GET CONSULTANT NAME ===')
  console.log('Consultant profile data:', consultantProfile.value)
  console.log('Consultant profile type:', typeof consultantProfile.value)

  if (consultantProfile.value) {
    console.log('Consultant profile keys:', Object.keys(consultantProfile.value))
    console.log('ConsultantName field:', consultantProfile.value.ConsultantName)
    console.log('Name field:', consultantProfile.value.Name)
    console.log('All consultant data:', JSON.stringify(consultantProfile.value, null, 2))
  }

  // Check nested profile structure
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.ConsultantName) {
    console.log('Using real consultant name from profile:', consultantProfile.value.profile.ConsultantName)
    return `Mr ${consultantProfile.value.profile.ConsultantName}`
  } else if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Name) {
    console.log('Using consultant name from profile Name field:', consultantProfile.value.profile.Name)
    return `Mr ${consultantProfile.value.profile.Name}`
  } else if (consultantProfile.value && consultantProfile.value.ConsultantName) {
    console.log('Using real consultant name (direct):', consultantProfile.value.ConsultantName)
    return `Mr ${consultantProfile.value.ConsultantName}`
  } else {
    console.log('No consultant data available, using fallback')
    return 'Mr Bob Yang' // Fallback while data is loading
  }
}

// Helper functions for consultant contact information
const getConsultantPhone = () => {
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Phone) {
    return consultantProfile.value.profile.Phone
  }
  return 'Not available'
}

const getConsultantEmail = () => {
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Email) {
    return consultantProfile.value.profile.Email
  }
  return 'Not available'
}

const getConsultantDepartment = () => {
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Department) {
    return consultantProfile.value.profile.Department
  }
  return 'Not available'
}

const getConsultantAvailable = () => {
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Available) {
    return consultantProfile.value.profile.Available
  }
  return 'Not available'
}

// Get user-friendly error message
const getErrorMessage = () => {
  switch (error.value) {
    case 'PATIENT_NOT_FOUND':
      return 'The patient consent record could not be found. The consent link may be invalid or expired.'
    case 'PROFILE_NOT_FOUND':
      return 'The healthcare provider information is not available. Please contact your healthcare provider.'
    case 'Patient document not found':
      return 'The patient consent record could not be found. The consent link may be invalid or expired.'
    case 'User profile not found':
      return 'The healthcare provider information is not available. Please contact your healthcare provider.'
    case 'No consent ID provided':
      return 'No consent ID was provided in the link. Please check the link and try again.'
    default:
      return error.value || 'An unexpected error occurred while loading the consent information.'
  }
}

// Set page title
useHead({
  title: 'Patient Consent | Surgassists'
})
</script>