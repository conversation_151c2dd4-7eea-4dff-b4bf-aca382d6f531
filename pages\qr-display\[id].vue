<template>
  <div class="min-h-screen flex items-center justify-center bg-white">
    <!-- Loading state -->
    <div v-if="loading" class="text-center">
      <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-4 text-gray-600">Loading QR code...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="text-center">
      <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100">
        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
      <h1 class="mt-4 text-2xl font-bold text-gray-900">Not Found</h1>
      <p class="mt-2 text-gray-600">{{ error }}</p>
    </div>

    <!-- QR Code Display -->
    <div v-else-if="qrData" class="text-center">
      <!-- Display QR code using base64 data -->
      <div v-if="qrData.qr_code_base64" class="mb-4">
        <img
          :src="`data:image/png;base64,${qrData.qr_code_base64}`"
          :alt="`QR Code for Patient ${qrData.patient_id}`"
          class="mx-auto max-w-lg w-full h-auto"
        />
      </div>

      <!-- Fallback to URL if base64 not available -->
      <div v-else-if="qrData.qr_code_url" class="mb-4">
        <img
          :src="qrData.qr_code_url"
          :alt="`QR Code for Patient ${qrData.patient_id}`"
          class="mx-auto max-w-lg w-full h-auto"
        />
      </div>

      <!-- QR code info (optional, can be removed for cleaner display) -->
      <div class="text-sm text-gray-500 mt-4">
        <p>Patient ID: {{ qrData.patient_id }}</p>
        <p v-if="qrData.qr_code_size">Size: {{ formatFileSize(qrData.qr_code_size) }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

// Get the route to access the ID parameter
const route = useRoute()
const qrId = route.params.id

// State
const qrData = ref(null)
const loading = ref(true)
const error = ref('')

// Load QR code data by ID using server-side API
const loadQRCode = async () => {
  try {
    loading.value = true
    error.value = ''

    console.log('Loading QR code for ID:', qrId)

    // Use server-side API to fetch QR code data securely
    const response = await $fetch(`/api/qr/${qrId}`)

    if (response.success && response.data) {
      qrData.value = response.data
      console.log('QR code data loaded:', response.data)
    } else {
      error.value = `No QR code found with ID: ${qrId}`
    }

  } catch (err) {
    console.error('Error loading QR code:', err)

    // Handle specific HTTP status codes
    if (err.statusCode === 404) {
      error.value = 'QR code not found'
    } else if (err.statusCode === 400) {
      error.value = 'Invalid QR code ID'
    } else if (err.statusCode === 401 || err.statusCode === 403) {
      error.value = 'Access denied'
    } else if (err.statusCode === 500) {
      error.value = 'Server error occurred'
    } else if (err.statusCode && err.statusCode !== 200) {
      // Any other non-200 status code
      error.value = 'Not found'
    } else {
      // Network or other errors
      error.value = 'Unable to load QR code'
    }
  } finally {
    loading.value = false
  }
}

// Format file size for display
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Load QR code when component mounts
onMounted(() => {
  if (qrId) {
    loadQRCode()
  } else {
    error.value = 'No QR ID provided in URL'
    loading.value = false
  }
})

// Set page title
useHead({
  title: `QR Code - ${qrId} | Surgassists`
})

// Make this page publicly accessible (no authentication required)
definePageMeta({
  auth: false
})
</script>