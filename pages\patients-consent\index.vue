<template>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

    <div class="mb-8">
      <div class="bg-gray-100 rounded-2xl p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Patient Consent</h1>
        <p class="text-gray-700">Patients Consent. Manage and collect patient consent forms</p>
      </div>
    </div>

    <!-- Start Consent Button -->
    <div class="mb-8">
      <button
        @click="showConsentForm = true"
        class="text-white px-6 py-3 rounded-md font-medium transition-colors hover:opacity-90"
        style="background-color: #65ADEE;">
        Start Consent
      </button>
    </div>

    <!-- Consent Form Modal -->
    <div v-if="showConsentForm" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Patient Consent Form
          </h3>

          <form @submit.prevent="handleConsentSubmit" class="space-y-4">
            <div>
              <label for="patientEmail" class="block text-sm font-medium text-gray-700">Select Patient</label>
              <select
                id="patientEmail"
                v-model="patientEmail"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a patient...</option>
                <option
                  v-for="patient in (patients || [])"
                  :key="patient.$id"
                  :value="patient.Email"
                >
                  {{ patient.Name }} ({{ patient.Email }})
                </option>
              </select>

              <!-- No patients message -->
              <div v-if="patients && patients.length === 0 && !patientsLoading" class="mt-2 text-sm text-gray-500">
                No patients found.
                <NuxtLink to="/patient-list-add" class="text-blue-600 hover:text-blue-800 underline">
                  Add patients first
                </NuxtLink>
                to create consent forms.
              </div>

              <!-- Loading patients -->
              <div v-if="patientsLoading" class="mt-2 text-sm text-gray-500">
                Loading patients...
              </div>
            </div>

            <div>
              <label for="documentSelection" class="block text-sm font-medium text-gray-700">
                Select Document
              </label>
              <select
                id="documentSelection"
                v-model="selectedDocument"
                required
                :disabled="!selectedPatientId || documentsLoading"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">
                  {{ !selectedPatientId ? 'Select a patient first' : documentsLoading ? 'Loading documents...' : 'Select a document' }}
                </option>
                <option
                  v-for="doc in patientDocuments"
                  :key="doc.$id"
                  :value="`${doc.ProcedureType}/${doc.Documents}`"
                >
                  {{ doc.ProcedureType }}/{{ doc.Documents }}
                </option>
              </select>

              <!-- No documents message -->
              <div v-if="selectedPatientId && patientDocuments.length === 0 && !documentsLoading" class="mt-2 text-sm text-gray-500">
                No documents found for this patient.
                <NuxtLink :to="`/create-document/${selectedPatientId}`" class="text-blue-600 hover:text-blue-800 underline">
                  Upload documents first
                </NuxtLink>
                to create consent forms.
              </div>
            </div>

            <!-- Error message -->
            <div v-if="error" class="text-red-500 text-sm">
              {{ error }}
            </div>

            <!-- Form buttons -->
            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="closeConsentForm"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="formLoading"
                class="px-4 py-2 text-white rounded-md text-sm font-medium transition-colors disabled:opacity-50 hover:opacity-90"
                style="background-color: #65ADEE;"
              >
                <span v-if="formLoading">Processing...</span>
                <span v-else>Start Consent Process</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Consent Records Section -->
    <div class="mt-8">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Consent Records</h2>
      <div class="bg-white rounded-lg shadow-md p-6">
        <!-- Loading state -->
        <div v-if="recordsLoading" class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="mt-2 text-gray-500">Loading consent records...</p>
        </div>

        <!-- Consent records list -->
        <div v-else-if="consentRecords.length > 0" class="space-y-4">
          <div
            v-for="record in consentRecords"
            :key="record.$id"
            class="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                  </svg>
                </div>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">{{ record.Email }}</p>
                <p class="text-xs text-gray-500">{{ formatDate(record.$createdAt) }}</p>
              </div>
            </div>
            <div class="flex items-center">
              <span :class="getStatusColor(record.Status)" class="px-2 py-1 text-xs font-medium rounded-full">
                {{ record.Status }}
              </span>
            </div>
          </div>
        </div>

        <!-- No records -->
        <div v-else class="text-center py-8">
          <p class="text-gray-500">No consent records found.</p>
          <p class="text-sm text-gray-400 mt-1">Click "Start Consent" to create your first consent record.</p>
        </div>
      </div>
    </div>
  </div>


    
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Protect this page - require authentication
definePageMeta({
  middleware: 'auth'
})

// Use auth composable
const { isLoggedIn, currentUser, checkAuth } = useAuth()
const { logAction, LOG_ACTIONS } = useLogger()

// Form state
const showConsentForm = ref(false)
const patientEmail = ref('')
const selectedDocument = ref('')
const error = ref('')
const formLoading = ref(false)

// Patients state
const patients = ref([])
const patientsLoading = ref(false)
const selectedPatientId = ref('')

// Patient documents state
const patientDocuments = ref([])
const documentsLoading = ref(false)

// Load patient documents when patient is selected
const loadPatientDocuments = async (patientId) => {
  if (!patientId) {
    patientDocuments.value = []
    return
  }

  try {
    documentsLoading.value = true
    console.log('Loading documents for patient:', patientId)

    // Call our server-side API endpoint
    const response = await $fetch(`/api/patient-documents/${patientId}`)

    console.log('Patient documents API response:', response)

    if (response.success && response.documents) {
      patientDocuments.value = response.documents
      console.log('Patient documents loaded:', patientDocuments.value)
    } else {
      console.log('No documents found for patient')
      patientDocuments.value = []
    }

  } catch (err) {
    console.error('Error loading patient documents:', err)
    patientDocuments.value = []
    // Don't show error to user - documents dropdown will show "No documents found"
  } finally {
    documentsLoading.value = false
  }
}

// Watch for patient selection changes
watch(patientEmail, async (newEmail) => {
  // Reset document selection when patient changes
  selectedDocument.value = ''

  if (newEmail) {
    // Find the selected patient to get their ID
    const selectedPatient = patients.value.find(p => p.Email === newEmail)
    if (selectedPatient) {
      selectedPatientId.value = selectedPatient.$id
      await loadPatientDocuments(selectedPatient.$id)
    }
  } else {
    selectedPatientId.value = ''
    patientDocuments.value = []
  }
})

// Consent records state
const consentRecords = ref([])
const recordsLoading = ref(false)

// Handle consent form submission
const handleConsentSubmit = async () => {
  try {
    formLoading.value = true
    error.value = ''

    if (!currentUser.value) {
      throw new Error('User not authenticated')
    }

    // Validate that both patient and document are selected
    if (!patientEmail.value || !selectedDocument.value) {
      throw new Error('Please select both a patient and a document')
    }

    // Find the selected patient to get their name
    const selectedPatient = patients.value.find(patient => patient.Email === patientEmail.value)
    if (!selectedPatient) {
      throw new Error('Selected patient not found')
    }

    // Extract procedure type from selected document (format: "ProcedureType/DocumentID")
    const [procedureType, documentId] = selectedDocument.value.split('/')
    if (!procedureType || !documentId) {
      throw new Error('Invalid document selection')
    }

    console.log('Selected patient:', selectedPatient.Name, '(' + selectedPatient.Email + ')')
    console.log('Selected document:', selectedDocument.value)
    console.log('Extracted procedure type:', procedureType)
    console.log('Extracted document ID:', documentId)

    // Save to Appwrite database with "Started" status
    const { appwriteService } = await import('~/services/appwrite')

    const consentRecord = await appwriteService.createConsent(
      currentUser.value.$id,
      patientEmail.value,
      selectedPatient.Name,  // Add the patient name
      procedureType,         // Extracted from selected document (goes to Procedure field)
      'Started',
      documentId             // Extracted document ID (goes to DocumentIDSent field)
    )
    
    await logAction(currentUser.value.$id, LOG_ACTIONS.CONSENT_STARTED)

    await loadConsentRecords()

    // Close form and reset
    closeConsentForm()

    console.log('Consent process started successfully!')

  } catch (err) {
    console.error('Consent form error:', err)
    error.value = err.message || 'Failed to start consent process. Please try again.'
  } finally {
    formLoading.value = false
  }
}


const closeConsentForm = () => {
  showConsentForm.value = false
  patientEmail.value = ''
  selectedDocument.value = ''
  selectedPatientId.value = ''
  patientDocuments.value = []
  error.value = ''
}


const loadPatients = async () => {
  try {
    patientsLoading.value = true

    const { appwriteService } = await import('~/services/appwrite')

    patients.value = await appwriteService.getPatients()

    console.log('Loaded patients:', patients.value.length)
  } catch (err) {
    console.error('Error loading patients:', err)
    patients.value = [] // Ensure it's always an array
  } finally {
    patientsLoading.value = false
  }
}


const loadConsentRecords = async () => {
  if (!currentUser.value) return

  try {
    recordsLoading.value = true
    const { appwriteService } = await import('~/services/appwrite')
    consentRecords.value = await appwriteService.getUserConsents(currentUser.value.$id)
    console.log('Loaded consent records:', consentRecords.value)
  } catch (err) {
    console.error('Error loading consent records:', err)
  } finally {
    recordsLoading.value = false
  }
}


const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now - date) / (1000 * 60))
    return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes} minutes ago`
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}


const getStatusColor = (status) => {
  switch (status) {
    case 'Started': return 'bg-blue-100 text-blue-800'
    case 'Patient Open Form': return 'bg-yellow-100 text-yellow-800'
    case 'Patient Consent Done': return 'bg-green-100 text-green-800'
    case 'QR Generated': return 'bg-indigo-100 text-indigo-800'
    case 'VR App Viewed': return 'bg-purple-100 text-purple-800'
    case 'Email Sent': return 'bg-orange-100 text-orange-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}


onMounted(async () => {
  await checkAuth()
  if (isLoggedIn.value && currentUser.value) {
    await logAction(currentUser.value.$id, LOG_ACTIONS.PATIENTS_CONSENT_VIEWED)

    await Promise.all([
      loadPatients(),
      loadConsentRecords()
    ])
  }
})
</script>
