export default defineEventHandler(async (event) => {
  try {
    // Get the consent ID from the URL parameter
    const consentId = getRouterParam(event, 'id')

    if (!consentId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Consent ID is required'
      })
    }

    // Get runtime config (server-side only)
    const config = useRuntimeConfig()

    // Make request to Go backend with proper Bearer Token authentication
    const response = await $fetch(`${config.goBackendApi}/get-pdf-documents-for-signature/${consentId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    // Process response silently

    // Return the PDF documents data
    return {
      success: true,
      data: response,
      message: 'PDF documents loaded successfully'
    }

  } catch (error) {
    // Handle error silently
    
    // Handle specific Go backend errors
    if (error.data && error.data.error) {
      throw createError({
        statusCode: error.status || 500,
        statusMessage: error.data.error
      })
    } else {
      throw createError({
        statusCode: error.status || error.statusCode || 500,
        statusMessage: error.message || 'Failed to fetch PDF documents'
      })
    }
  }
})
