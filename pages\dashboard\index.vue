<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Loading state -->
    <div v-if="isLoading" class="text-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-4 text-gray-600">Loading dashboard...</p>
    </div>

    <!-- Not logged in -->
    <div v-else-if="!isLoggedIn" class="text-center">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Access Denied</h1>
      <p class="text-gray-600 mb-8">You need to be logged in to access the dashboard.</p>
      <NuxtLink
        to="/login"
        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
      >
        Go to Login
      </NuxtLink>
    </div>

    <!-- Dashboard content -->
    <div v-else>
      <div class="mb-8">
        <div class="rounded-lg p-6 text-white shadow-md" style="background-color: #65ADEE;">
          <p class="text-lg font-medium">
            {{ getTimeBasedGreeting() }}, {{ currentUser?.name || currentUser?.email }}!
          </p>
          <p class="mt-1 text-white/90">
        Here's your activity overview for today.
          </p>

          <!-- Statistics Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
            <!-- Today's Sessions -->
            <div class="rounded-lg p-4 text-white text-center" style="background-color: #94C6F3;">
              <div class="text-2xl font-bold mb-1">12</div>
              <div class="text-xs opacity-90">Today's Sessions Today</div>
            </div>

            <!-- Total Patients Consented -->
            <div class="rounded-lg p-4 text-white text-center" style="background-color: #94C6F3;">
              <div class="text-2xl font-bold mb-1">2248</div>
              <div class="text-xs opacity-90">Total Patients All Time Consented</div>
            </div>

            <!-- Consent Completed Today -->
            <div class="rounded-lg p-4 text-white text-center" style="background-color: #94C6F3;">
              <div class="text-2xl font-bold mb-1">4/12</div>
              <div class="text-xs opacity-90">Consent Completed Today</div>
            </div>

            <!-- Average Session Time -->
            <div class="rounded-lg p-4 text-white text-center" style="background-color: #94C6F3;">
              <div class="text-2xl font-bold mb-1">2.8 min</div>
              <div class="text-xs opacity-90">Avg. Session</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Dashboard cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Profile Card -->
        <NuxtLink to="/profile">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">Profile</h3>
                <p class="text-sm text-gray-500">Manage your account settings</p>
              </div>
            </div>
          </div>
        </NuxtLink>

        <!-- Appointments Card -->
        <NuxtLink to="/patients-consent">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">Patients Consent</h3>
                <p class="text-sm text-gray-500">View and manage consents for patients</p>
              </div>
            </div>
          </div>
        </NuxtLink>


        <!-- Reports Card -->
       <NuxtLink to="/reports">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">Reports</h3>
                <p class="text-sm text-gray-500">View analytics and reports</p>
              </div>
            </div>
          </div>
          </NuxtLink>


        </div>

      <!-- Additional Cards Row -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        <!-- Schedule Session Card -->
        <NuxtLink to="/schedule-session">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">Schedule Session</h3>
                <p class="text-sm text-gray-500">Schedule and manage sessions</p>
              </div>
            </div>
          </div>
        </NuxtLink>

        <!-- Add Patient Card -->
        <NuxtLink to="/patient-list-add">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">Add Patient</h3>
                <p class="text-sm text-gray-500">Add and manage patient records</p>
              </div>
            </div>
          </div>
        </NuxtLink>
      </div>

      <!-- Recent Activity -->
      <div class="mt-8">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-4">
            <h2 class="text-xl font-semibold text-gray-900">Recent Activity</h2>
            <NuxtLink
              to="/activity-all"
              class="text-sm text-blue-600 hover:text-blue-700 hover:underline"
            >
              View All Activity
            </NuxtLink>
          </div>
          <button
            v-if="recentActivity.length > 0"
            @click="handleClearLogs"
            :disabled="clearingLogs"
            class="text-xs bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md transition-colors disabled:opacity-50"
          >
            <span v-if="clearingLogs">Clearing...</span>
            <span v-else>Clear All</span>
          </button>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <!-- Loading activity -->
          <div v-if="activityLoading" class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p class="mt-2 text-gray-500">Loading activity...</p>
          </div>

          <!-- Activity list -->
          <div v-else-if="recentActivity.length > 0" class="space-y-4">
            <div
              v-for="activity in recentActivity"
              :key="activity.$id"
              class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">{{ activity.LogAction }}</p>
                  <div class="flex items-center space-x-2 text-xs text-gray-500">
                    <span>{{ formatDate(activity.$createdAt) }}</span>
                    <span v-if="activity.IPAddress" class="flex items-center">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                      </svg>
                      {{ activity.IPAddress }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- No activity -->
          <div v-else class="text-center py-8">
            <p class="text-gray-500">No recent activity to display.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Use auth composable
const { isLoggedIn, currentUser, isLoading, checkAuth } = useAuth()

// Time-based greeting function
const getTimeBasedGreeting = () => {
  const hour = new Date().getHours()

  if (hour < 12) {
    return 'Good Morning'
  } else if (hour < 17) {
    return 'Good Afternoon'
  } else {
    return 'Good Evening'
  }
}

// Use logger composable
const { getUserActivity, logAction, clearUserActivity, LOG_ACTIONS } = useLogger()

// Activity state
const recentActivity = ref([])
const activityLoading = ref(false)
const clearingLogs = ref(false)

// Load user activity
const loadActivity = async () => {
  if (!currentUser.value) return

  try {
    activityLoading.value = true
    recentActivity.value = await getUserActivity(currentUser.value.$id, 20)
  } catch (error) {
    console.error('Error loading activity:', error)
  } finally {
    activityLoading.value = false
  }
}

// Clear all user logs
const handleClearLogs = async () => {
  if (!currentUser.value) return

  try {
    clearingLogs.value = true
    await clearUserActivity(currentUser.value.$id)

    // Log the clear action
    await logAction(currentUser.value.$id, 'Activity log cleared')

    // Reload activity to show the new clear log entry
    await loadActivity()
  } catch (error) {
    console.error('Error clearing logs:', error)
    // You could add a toast notification here for user feedback
  } finally {
    clearingLogs.value = false
  }
}

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now - date) / (1000 * 60))
    return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes} minutes ago`
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}

// Check authentication and load data when component mounts
onMounted(async () => {
  await checkAuth()
  if (isLoggedIn.value && currentUser.value) {
    // Log dashboard view
    await logAction(currentUser.value.$id, LOG_ACTIONS.DASHBOARD_VIEWED)
    // Load recent activity
    await loadActivity()
  }
})
</script>
