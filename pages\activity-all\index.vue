<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

    <div class="mb-8">
      <div class="bg-gray-100 rounded-2xl p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">All Activity</h1>
        <p class="text-gray-700">Complete history of your account activity </p>
      </div>
    </div>  

    <!-- All Activity -->
    <div>
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-gray-900">Activity History</h2>
        <button
          v-if="allActivity.length > 0"
          @click="handleClearLogs"
          :disabled="clearingLogs"
          class="text-xs bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md transition-colors disabled:opacity-50"
        >
          <span v-if="clearingLogs">Clearing...</span>
          <span v-else>Clear All</span>
        </button>
      </div>

      <div class="bg-white rounded-lg shadow-md p-6">
        <!-- Loading activity -->
        <div v-if="activityLoading" class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="mt-2 text-gray-500">Loading activity...</p>
        </div>

        <!-- Activity list -->
        <div v-else-if="allActivity.length > 0" class="space-y-4">
          <div
            v-for="activity in allActivity"
            :key="activity.$id"
            class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">{{ activity.LogAction }}</p>
                <div class="flex items-center space-x-2 text-xs text-gray-500">
                  <span>{{ formatDate(activity.$createdAt) }}</span>
                  <span v-if="activity.IPAddress" class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                    </svg>
                    {{ activity.IPAddress }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- No activity -->
        <div v-else class="text-center py-8">
          <p class="text-gray-500">No activity to display.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Protect this page - require authentication
definePageMeta({
  middleware: 'auth'
})

// Use auth composable
const { isLoggedIn, currentUser, checkAuth } = useAuth()

// Use logger composable
const { getUserActivity, logAction, clearUserActivity, LOG_ACTIONS } = useLogger()

// Activity state
const allActivity = ref([])
const activityLoading = ref(false)
const clearingLogs = ref(false)

// Load all user activity (no limit)
const loadAllActivity = async () => {
  if (!currentUser.value) return

  try {
    activityLoading.value = true
    // Load all activity by passing a very high limit
    allActivity.value = await getUserActivity(currentUser.value.$id, 1000)
  } catch (error) {
    console.error('Error loading all activity:', error)
  } finally {
    activityLoading.value = false
  }
}

// Clear all logs
const handleClearLogs = async () => {
  if (!currentUser.value) return

  const confirmed = confirm('Are you sure you want to clear all activity logs? This action cannot be undone.')
  if (!confirmed) return

  try {
    clearingLogs.value = true
    await clearUserActivity(currentUser.value.$id)

    // Log the clear action
    await logAction(currentUser.value.$id, 'Cleared all activity logs')

    // Reload activity
    await loadAllActivity()
  } catch (error) {
    console.error('Error clearing logs:', error)
    alert('Failed to clear logs. Please try again.')
  } finally {
    clearingLogs.value = false
  }
}

// Format date helper
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Check authentication and load data when component mounts
onMounted(async () => {
  await checkAuth()
  if (isLoggedIn.value && currentUser.value) {
    // Log activity all page view
    await logAction(currentUser.value.$id, 'Viewed all activity page')
    // Load all activity
    await loadAllActivity()
  }
})

// Set page title
useHead({
  title: 'All Activity | Surgassists'
})
</script>